using System.Linq;
using UnityEditor;
using UnityEngine.UIElements;

public static class EditorDialogueFeatures
{
    public static void RefreshView(ViewDialogueTree treeView, ViewDialogueInspector inspectorView, Label dialogueTreeIDLabel) // 刷新视图
    {
        // 保存当前选中的节点
        ViewDialogueNode selectedNode = null;
        if (treeView != null && treeView.selection.Count > 0)
        {
            selectedNode = treeView.selection[0] as ViewDialogueNode;
        }

        // 刷新视图
        DialogueTreeSO tree = Selection.activeObject as DialogueTreeSO;
        if (tree)
        {
            treeView.PopulateView(tree);
            treeView.EnsureRootNodeExists(tree);
            dialogueTreeIDLabel.text = tree.name;
        }

        // 恢复选中节点状态
        if (selectedNode != null)
        {
            var nodeView = treeView.FindNodeView(selectedNode.node);
            if (nodeView != null)
            {
                treeView.AddToSelection(nodeView);
                inspectorView.UpdateSelection(nodeView);
            }
        }
    }

    public static void VerticalSort(ViewDialogueTree treeView) // 垂直排序
    {
        var selectedNodes = treeView.selection.OfType<ViewDialogueNode>().ToList();
        if (selectedNodes.Count == 0) return;

        // 按照节点的Y坐标排序
        selectedNodes.Sort((a, b) => a.GetPosition().y.CompareTo(b.GetPosition().y));

        // 获取基础位置节点
        var baseNode = selectedNodes.First();
        var basePosition = baseNode.GetPosition().position;
        var baseWidth = baseNode.GetPosition().width;

        // 设置节点间距
        float spacing = 41f;

        // 计算并设置每个节点的新位置
        float currentY = basePosition.y;
        foreach (var node in selectedNodes)
        {
            var position = node.GetPosition();
            position.x = basePosition.x + (baseWidth - position.width) / 2; // 垂直轴线对齐
            position.y = currentY; // 设置当前节点的Y坐标

            node.SetPosition(position);

            // 更新当前Y坐标，考虑节点高度和间距
            currentY += position.height + spacing;
        }
    }
}
