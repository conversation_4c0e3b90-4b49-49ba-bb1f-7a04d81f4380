Using pre-set license
Built from '2022.3/china_unity/release' branch; Version is '2022.3.33f1c1 (ea5182f68133) revision 15356290'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit Professional' Language: 'zh' Physical Memory: 16280 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
F:/The Lightless Crown
-logFile
Logs/AssetImportWorker0.log
-srvPort
60629
Successfully changed project path to: F:/The Lightless Crown
F:/The Lightless Crown
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [34788] Host "[IP] *********** [Port] 0 [Flags] 2 [Guid] 3870923489 [EditorId] 3870923489 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-NEVNEH0) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined multi-casting on [***********:54997]...

Player connection [34788] Host "[IP] *********** [Port] 0 [Flags] 2 [Guid] 3870923489 [EditorId] 3870923489 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-NEVNEH0) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined alternative multi-casting on [***********:34997]...

[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
Refreshing native plugins compatible for Editor in 118.32 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2022.3.33f1c1 (ea5182f68133)
[Subsystems] Discovering subsystems at path D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path F:/The Lightless Crown/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce GTX 960 (ID=0x1401)
    Vendor:   NVIDIA
    VRAM:     4040 MB
    Driver:   32.0.15.6094
Initialize mono
Mono path[0] = 'D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed'
Mono path[1] = 'D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56392
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.025722 seconds.
- Loaded All Assemblies, in  0.654 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.453 seconds
Domain Reload Profiling: 1099ms
	BeginReloadAssembly (202ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (65ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (133ms)
	LoadAllAssembliesAndSetupDomain (228ms)
		LoadAssemblies (192ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (224ms)
			TypeCache.Refresh (222ms)
				TypeCache.ScanAssembly (201ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (454ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (362ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (18ms)
			SetLoadedEditorAssemblies (11ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (3ms)
			ProcessInitializeOnLoadAttributes (246ms)
			ProcessInitializeOnLoadMethodAttributes (84ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.564 seconds
Refreshing native plugins compatible for Editor in 18.87 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.478 seconds
Domain Reload Profiling: 4025ms
	BeginReloadAssembly (318ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (50ms)
	RebuildCommonClasses (57ms)
	RebuildNativeTypeToScriptingClass (16ms)
	initialDomainReloadingComplete (96ms)
	LoadAllAssembliesAndSetupDomain (1059ms)
		LoadAssemblies (824ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (425ms)
			TypeCache.Refresh (367ms)
				TypeCache.ScanAssembly (341ms)
			ScanForSourceGeneratedMonoScriptInfo (39ms)
			ResolveRequiredComponents (17ms)
	FinalizeReload (2479ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2203ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (10ms)
			SetLoadedEditorAssemblies (6ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (135ms)
			ProcessInitializeOnLoadAttributes (1165ms)
			ProcessInitializeOnLoadMethodAttributes (853ms)
			AfterProcessingInitializeOnLoad (34ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (17ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.12 seconds
Refreshing native plugins compatible for Editor in 20.59 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5633 Unused Serialized files (Serialized files now loaded: 0)
Unloading 81 unused Assets / (352.3 KB). Loaded Objects now: 6101.
Memory consumption went from 215.6 MB to 215.3 MB.
Total: 8.556400 ms (FindLiveObjects: 0.540900 ms CreateObjectMapping: 0.411200 ms MarkObjects: 7.152600 ms  DeleteObjects: 0.450600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 160019.423726 seconds.
  path: Assets/Resources/Data/Dialogue/DH00123.asset
  artifactKey: Guid(684a21f6bb28d1342aca907487c91657) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/Dialogue/DH00123.asset using Guid(684a21f6bb28d1342aca907487c91657) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: 'ed3063433289fa84935af1469d6adcf7') in 0.054554 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 11
========================================================================
Received Import Request.
  Time since last request: 100.745276 seconds.
  path: Assets/Resources/Data/Roles/R00000.asset
  artifactKey: Guid(c8440add6f2557b4eabd0b723f366f1a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/Roles/R00000.asset using Guid(c8440add6f2557b4eabd0b723f366f1a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: 'd30af0d7f6cd41273546855e9cd94255') in 0.003097 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 1327.557127 seconds.
  path: Assets/Scripts/Test/LanguageManagerTest.cs
  artifactKey: Guid(b273e7726b4fd55498c04a5242688236) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Test/LanguageManagerTest.cs using Guid(b273e7726b4fd55498c04a5242688236) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '0f8428ee68edd7d628635a80c23b4911') in 0.001568 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 52.827660 seconds.
  path: Assets/Scripts/Manager/AudioManager.cs
  artifactKey: Guid(9fac522209a037f439d5c36644915d13) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Manager/AudioManager.cs using Guid(9fac522209a037f439d5c36644915d13) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '44f0b97a6a3ed4a2667e5fa6b162aee6') in 0.000830 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 3.522395 seconds.
  path: Assets/Scripts/Manager/LanguageManager.cs
  artifactKey: Guid(5f31673f0da53e747b3aa667bf5bfc77) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Manager/LanguageManager.cs using Guid(5f31673f0da53e747b3aa667bf5bfc77) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '36e4ee840d2e5cdf4c9c9ab981148fa1') in 0.000794 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.359 seconds
Refreshing native plugins compatible for Editor in 20.48 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  3.186 seconds
Domain Reload Profiling: 4530ms
	BeginReloadAssembly (282ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (79ms)
	RebuildCommonClasses (46ms)
	RebuildNativeTypeToScriptingClass (16ms)
	initialDomainReloadingComplete (70ms)
	LoadAllAssembliesAndSetupDomain (930ms)
		LoadAssemblies (1018ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (61ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (4ms)
			ScanForSourceGeneratedMonoScriptInfo (30ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (3187ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1487ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (10ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (111ms)
			ProcessInitializeOnLoadAttributes (681ms)
			ProcessInitializeOnLoadMethodAttributes (658ms)
			AfterProcessingInitializeOnLoad (23ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (27ms)
Refreshing native plugins compatible for Editor in 16.37 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5480 Unused Serialized files (Serialized files now loaded: 0)
Unloading 58 unused Assets / (322.2 KB). Loaded Objects now: 6107.
Memory consumption went from 189.6 MB to 189.3 MB.
Total: 5.977500 ms (FindLiveObjects: 0.513600 ms CreateObjectMapping: 0.301300 ms MarkObjects: 5.019500 ms  DeleteObjects: 0.142100 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  3.262 seconds
Refreshing native plugins compatible for Editor in 58.82 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in 13.537 seconds
Domain Reload Profiling: 16755ms
	BeginReloadAssembly (852ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (29ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (224ms)
	RebuildCommonClasses (153ms)
	RebuildNativeTypeToScriptingClass (51ms)
	initialDomainReloadingComplete (237ms)
	LoadAllAssembliesAndSetupDomain (1925ms)
		LoadAssemblies (2302ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (92ms)
			TypeCache.Refresh (20ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (71ms)
	FinalizeReload (13538ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1870ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (11ms)
			SetLoadedEditorAssemblies (6ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (209ms)
			ProcessInitializeOnLoadAttributes (827ms)
			ProcessInitializeOnLoadMethodAttributes (772ms)
			AfterProcessingInitializeOnLoad (46ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (29ms)
Refreshing native plugins compatible for Editor in 121.52 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5480 Unused Serialized files (Serialized files now loaded: 0)
Unloading 56 unused Assets / (321.7 KB). Loaded Objects now: 6111.
Memory consumption went from 189.6 MB to 189.3 MB.
Total: 28.157900 ms (FindLiveObjects: 2.923500 ms CreateObjectMapping: 1.668700 ms MarkObjects: 23.035800 ms  DeleteObjects: 0.528300 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.855 seconds
Refreshing native plugins compatible for Editor in 39.26 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  6.338 seconds
Domain Reload Profiling: 9168ms
	BeginReloadAssembly (1244ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (48ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (301ms)
	RebuildCommonClasses (234ms)
	RebuildNativeTypeToScriptingClass (75ms)
	initialDomainReloadingComplete (313ms)
	LoadAllAssembliesAndSetupDomain (963ms)
		LoadAssemblies (1552ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (55ms)
			TypeCache.Refresh (18ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (34ms)
	FinalizeReload (6339ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (3131ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (12ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (217ms)
			ProcessInitializeOnLoadAttributes (1507ms)
			ProcessInitializeOnLoadMethodAttributes (1306ms)
			AfterProcessingInitializeOnLoad (70ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (63ms)
Refreshing native plugins compatible for Editor in 31.78 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5480 Unused Serialized files (Serialized files now loaded: 0)
Unloading 56 unused Assets / (320.5 KB). Loaded Objects now: 6115.
Memory consumption went from 189.6 MB to 189.3 MB.
Total: 9.334700 ms (FindLiveObjects: 1.118300 ms CreateObjectMapping: 0.378200 ms MarkObjects: 7.702000 ms  DeleteObjects: 0.135100 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.401 seconds
Refreshing native plugins compatible for Editor in 17.03 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  5.076 seconds
Domain Reload Profiling: 6447ms
	BeginReloadAssembly (380ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (83ms)
	RebuildCommonClasses (104ms)
	RebuildNativeTypeToScriptingClass (30ms)
	initialDomainReloadingComplete (97ms)
	LoadAllAssembliesAndSetupDomain (759ms)
		LoadAssemblies (788ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (194ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (4ms)
			ScanForSourceGeneratedMonoScriptInfo (141ms)
			ResolveRequiredComponents (34ms)
	FinalizeReload (5077ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (3153ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (10ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (149ms)
			ProcessInitializeOnLoadAttributes (1463ms)
			ProcessInitializeOnLoadMethodAttributes (1466ms)
			AfterProcessingInitializeOnLoad (60ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (49ms)
Refreshing native plugins compatible for Editor in 30.65 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5480 Unused Serialized files (Serialized files now loaded: 0)
Unloading 56 unused Assets / (320.6 KB). Loaded Objects now: 6119.
Memory consumption went from 189.6 MB to 189.3 MB.
Total: 24.812500 ms (FindLiveObjects: 1.582100 ms CreateObjectMapping: 1.915100 ms MarkObjects: 20.873800 ms  DeleteObjects: 0.439700 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 1623.982510 seconds.
  path: Assets/streamingAssets/language/简体中文.tsv
  artifactKey: Guid(0fab71915fcd47b479848f352513e2d4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/streamingAssets/language/简体中文.tsv using Guid(0fab71915fcd47b479848f352513e2d4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: 'a4fe0a56f65a57e2a6268dedac1f1fc6') in 0.027899 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 402.493403 seconds.
  path: Assets/streamingAssets/language/English.csv
  artifactKey: Guid(59cb6edb8c3f15c4aa00fdf92561d96b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/streamingAssets/language/English.csv using Guid(59cb6edb8c3f15c4aa00fdf92561d96b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: 'dff4fd4a1e839dba50cb0f999b377c21') in 0.002284 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.276 seconds
Refreshing native plugins compatible for Editor in 17.56 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  3.711 seconds
Domain Reload Profiling: 4962ms
	BeginReloadAssembly (394ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (80ms)
	RebuildCommonClasses (71ms)
	RebuildNativeTypeToScriptingClass (103ms)
	initialDomainReloadingComplete (98ms)
	LoadAllAssembliesAndSetupDomain (584ms)
		LoadAssemblies (735ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (66ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (33ms)
			ResolveRequiredComponents (16ms)
	FinalizeReload (3712ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1861ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (10ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (112ms)
			ProcessInitializeOnLoadAttributes (858ms)
			ProcessInitializeOnLoadMethodAttributes (812ms)
			AfterProcessingInitializeOnLoad (65ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (47ms)
Refreshing native plugins compatible for Editor in 31.84 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5480 Unused Serialized files (Serialized files now loaded: 0)
Unloading 56 unused Assets / (321.7 KB). Loaded Objects now: 6123.
Memory consumption went from 189.6 MB to 189.3 MB.
Total: 5.716700 ms (FindLiveObjects: 0.534400 ms CreateObjectMapping: 0.312200 ms MarkObjects: 4.572900 ms  DeleteObjects: 0.295300 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.380 seconds
Refreshing native plugins compatible for Editor in 23.04 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in 12.119 seconds
Domain Reload Profiling: 13473ms
	BeginReloadAssembly (386ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (102ms)
	RebuildCommonClasses (61ms)
	RebuildNativeTypeToScriptingClass (27ms)
	initialDomainReloadingComplete (121ms)
	LoadAllAssembliesAndSetupDomain (760ms)
		LoadAssemblies (871ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (85ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (35ms)
			ResolveRequiredComponents (34ms)
	FinalizeReload (12119ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2895ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (30ms)
			SetLoadedEditorAssemblies (18ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (239ms)
			ProcessInitializeOnLoadAttributes (1581ms)
			ProcessInitializeOnLoadMethodAttributes (1001ms)
			AfterProcessingInitializeOnLoad (26ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (26ms)
Refreshing native plugins compatible for Editor in 36.27 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5480 Unused Serialized files (Serialized files now loaded: 0)
Unloading 56 unused Assets / (320.7 KB). Loaded Objects now: 6127.
Memory consumption went from 189.6 MB to 189.3 MB.
Total: 30.783900 ms (FindLiveObjects: 3.399700 ms CreateObjectMapping: 7.434300 ms MarkObjects: 19.164500 ms  DeleteObjects: 0.783800 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.240 seconds
Refreshing native plugins compatible for Editor in 21.56 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  4.116 seconds
Domain Reload Profiling: 5339ms
	BeginReloadAssembly (414ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (141ms)
	RebuildCommonClasses (51ms)
	RebuildNativeTypeToScriptingClass (18ms)
	initialDomainReloadingComplete (104ms)
	LoadAllAssembliesAndSetupDomain (635ms)
		LoadAssemblies (715ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (79ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (41ms)
			ResolveRequiredComponents (18ms)
	FinalizeReload (4117ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1976ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (11ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (132ms)
			ProcessInitializeOnLoadAttributes (963ms)
			ProcessInitializeOnLoadMethodAttributes (839ms)
			AfterProcessingInitializeOnLoad (26ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (32ms)
Refreshing native plugins compatible for Editor in 61.37 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5480 Unused Serialized files (Serialized files now loaded: 0)
Unloading 56 unused Assets / (320.7 KB). Loaded Objects now: 6131.
Memory consumption went from 189.6 MB to 189.3 MB.
Total: 5.744900 ms (FindLiveObjects: 0.572800 ms CreateObjectMapping: 0.363000 ms MarkObjects: 4.665500 ms  DeleteObjects: 0.142600 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 2575.807679 seconds.
  path: Assets/Resources/Data/Dialogue/DH00123.asset
  artifactKey: Guid(684a21f6bb28d1342aca907487c91657) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/Dialogue/DH00123.asset using Guid(684a21f6bb28d1342aca907487c91657) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '6a37f7c40db8d70105f458242016714c') in 0.068597 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 11
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.471 seconds
Refreshing native plugins compatible for Editor in 17.30 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  4.530 seconds
Domain Reload Profiling: 5985ms
	BeginReloadAssembly (387ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (151ms)
	RebuildCommonClasses (48ms)
	RebuildNativeTypeToScriptingClass (18ms)
	initialDomainReloadingComplete (68ms)
	LoadAllAssembliesAndSetupDomain (933ms)
		LoadAssemblies (1031ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (65ms)
			TypeCache.Refresh (13ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (33ms)
			ResolveRequiredComponents (16ms)
	FinalizeReload (4531ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1983ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (6ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (148ms)
			ProcessInitializeOnLoadAttributes (890ms)
			ProcessInitializeOnLoadMethodAttributes (890ms)
			AfterProcessingInitializeOnLoad (37ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (34ms)
Refreshing native plugins compatible for Editor in 19.99 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5480 Unused Serialized files (Serialized files now loaded: 0)
Unloading 58 unused Assets / (323.3 KB). Loaded Objects now: 6135.
Memory consumption went from 189.6 MB to 189.3 MB.
Total: 9.896300 ms (FindLiveObjects: 0.515200 ms CreateObjectMapping: 0.429300 ms MarkObjects: 8.750200 ms  DeleteObjects: 0.200000 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 522.852508 seconds.
  path: Assets/Resources/Data/Dialogue/DH00123.asset
  artifactKey: Guid(684a21f6bb28d1342aca907487c91657) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/Dialogue/DH00123.asset using Guid(684a21f6bb28d1342aca907487c91657) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '7fe95304938f38aaaf2b226a575d3ee8') in 0.043587 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 11
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.031 seconds
Refreshing native plugins compatible for Editor in 29.83 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  4.246 seconds
Domain Reload Profiling: 6255ms
	BeginReloadAssembly (392ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (77ms)
	RebuildCommonClasses (51ms)
	RebuildNativeTypeToScriptingClass (18ms)
	initialDomainReloadingComplete (181ms)
	LoadAllAssembliesAndSetupDomain (1367ms)
		LoadAssemblies (1341ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (262ms)
			TypeCache.Refresh (29ms)
				TypeCache.ScanAssembly (1ms)
			ScanForSourceGeneratedMonoScriptInfo (177ms)
			ResolveRequiredComponents (51ms)
	FinalizeReload (4246ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1881ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (6ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (137ms)
			ProcessInitializeOnLoadAttributes (822ms)
			ProcessInitializeOnLoadMethodAttributes (880ms)
			AfterProcessingInitializeOnLoad (24ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (31ms)
Refreshing native plugins compatible for Editor in 16.78 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5480 Unused Serialized files (Serialized files now loaded: 0)
Unloading 58 unused Assets / (322.2 KB). Loaded Objects now: 6139.
Memory consumption went from 189.6 MB to 189.3 MB.
Total: 6.848100 ms (FindLiveObjects: 0.715100 ms CreateObjectMapping: 0.588200 ms MarkObjects: 5.376000 ms  DeleteObjects: 0.167200 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.535 seconds
Refreshing native plugins compatible for Editor in 46.81 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  4.775 seconds
Domain Reload Profiling: 6294ms
	BeginReloadAssembly (398ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (83ms)
	RebuildCommonClasses (52ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (78ms)
	LoadAllAssembliesAndSetupDomain (974ms)
		LoadAssemblies (1032ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (165ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (99ms)
			ResolveRequiredComponents (47ms)
	FinalizeReload (4776ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2128ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (16ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (153ms)
			ProcessInitializeOnLoadAttributes (1114ms)
			ProcessInitializeOnLoadMethodAttributes (810ms)
			AfterProcessingInitializeOnLoad (29ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (33ms)
Refreshing native plugins compatible for Editor in 18.35 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5480 Unused Serialized files (Serialized files now loaded: 0)
Unloading 56 unused Assets / (320.8 KB). Loaded Objects now: 6143.
Memory consumption went from 189.6 MB to 189.3 MB.
Total: 6.983400 ms (FindLiveObjects: 0.547400 ms CreateObjectMapping: 0.342100 ms MarkObjects: 5.959600 ms  DeleteObjects: 0.133100 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.306 seconds
Refreshing native plugins compatible for Editor in 23.41 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  3.730 seconds
Domain Reload Profiling: 5018ms
	BeginReloadAssembly (352ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (70ms)
	RebuildCommonClasses (57ms)
	RebuildNativeTypeToScriptingClass (22ms)
	initialDomainReloadingComplete (109ms)
	LoadAllAssembliesAndSetupDomain (748ms)
		LoadAssemblies (885ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (66ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (35ms)
			ResolveRequiredComponents (16ms)
	FinalizeReload (3730ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1862ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (11ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (126ms)
			ProcessInitializeOnLoadAttributes (881ms)
			ProcessInitializeOnLoadMethodAttributes (813ms)
			AfterProcessingInitializeOnLoad (26ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (35ms)
Refreshing native plugins compatible for Editor in 19.06 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5480 Unused Serialized files (Serialized files now loaded: 0)
Unloading 56 unused Assets / (321.7 KB). Loaded Objects now: 6147.
Memory consumption went from 189.6 MB to 189.3 MB.
Total: 13.920800 ms (FindLiveObjects: 0.580600 ms CreateObjectMapping: 0.593700 ms MarkObjects: 12.564800 ms  DeleteObjects: 0.179500 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.488 seconds
Refreshing native plugins compatible for Editor in 18.46 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  5.595 seconds
Domain Reload Profiling: 7045ms
	BeginReloadAssembly (488ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (167ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (73ms)
	RebuildCommonClasses (65ms)
	RebuildNativeTypeToScriptingClass (19ms)
	initialDomainReloadingComplete (100ms)
	LoadAllAssembliesAndSetupDomain (777ms)
		LoadAssemblies (893ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (68ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (36ms)
			ResolveRequiredComponents (16ms)
	FinalizeReload (5596ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (3121ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (30ms)
			SetLoadedEditorAssemblies (12ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (216ms)
			ProcessInitializeOnLoadAttributes (1404ms)
			ProcessInitializeOnLoadMethodAttributes (1424ms)
			AfterProcessingInitializeOnLoad (35ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (31ms)
Refreshing native plugins compatible for Editor in 76.10 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5480 Unused Serialized files (Serialized files now loaded: 0)
Unloading 56 unused Assets / (320.6 KB). Loaded Objects now: 6151.
Memory consumption went from 189.6 MB to 189.3 MB.
Total: 6.721700 ms (FindLiveObjects: 0.536900 ms CreateObjectMapping: 0.410400 ms MarkObjects: 5.631500 ms  DeleteObjects: 0.141100 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.199 seconds
Refreshing native plugins compatible for Editor in 16.85 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  7.037 seconds
Domain Reload Profiling: 8196ms
	BeginReloadAssembly (339ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (72ms)
	RebuildCommonClasses (82ms)
	RebuildNativeTypeToScriptingClass (26ms)
	initialDomainReloadingComplete (86ms)
	LoadAllAssembliesAndSetupDomain (626ms)
		LoadAssemblies (747ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (67ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (34ms)
			ResolveRequiredComponents (16ms)
	FinalizeReload (7038ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (4099ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (15ms)
			SetLoadedEditorAssemblies (6ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (380ms)
			ProcessInitializeOnLoadAttributes (1743ms)
			ProcessInitializeOnLoadMethodAttributes (1850ms)
			AfterProcessingInitializeOnLoad (106ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (52ms)
Refreshing native plugins compatible for Editor in 23.52 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5480 Unused Serialized files (Serialized files now loaded: 0)
Unloading 56 unused Assets / (320.7 KB). Loaded Objects now: 6155.
Memory consumption went from 189.6 MB to 189.3 MB.
Total: 8.255300 ms (FindLiveObjects: 2.482100 ms CreateObjectMapping: 0.604700 ms MarkObjects: 5.026800 ms  DeleteObjects: 0.140500 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 800.449725 seconds.
  path: Assets/Resources/Data/Dialogue/DH00123.asset
  artifactKey: Guid(684a21f6bb28d1342aca907487c91657) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/Dialogue/DH00123.asset using Guid(684a21f6bb28d1342aca907487c91657) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '0f03b4dc9867b85bec8bd49180c9d491') in 0.048200 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 11
========================================================================
Received Import Request.
  Time since last request: 146.991509 seconds.
  path: Assets/Scripts/Manager/LanguageManager.cs
  artifactKey: Guid(5f31673f0da53e747b3aa667bf5bfc77) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Manager/LanguageManager.cs using Guid(5f31673f0da53e747b3aa667bf5bfc77) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '7c53bb80c0f26321b30be32b0b179de8') in 0.001402 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  3.017 seconds
Refreshing native plugins compatible for Editor in 50.20 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  4.222 seconds
Domain Reload Profiling: 7194ms
	BeginReloadAssembly (642ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (92ms)
	RebuildCommonClasses (126ms)
	RebuildNativeTypeToScriptingClass (43ms)
	initialDomainReloadingComplete (215ms)
	LoadAllAssembliesAndSetupDomain (1945ms)
		LoadAssemblies (1990ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (216ms)
			TypeCache.Refresh (39ms)
				TypeCache.ScanAssembly (10ms)
			ScanForSourceGeneratedMonoScriptInfo (99ms)
			ResolveRequiredComponents (70ms)
	FinalizeReload (4223ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2068ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (13ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (145ms)
			ProcessInitializeOnLoadAttributes (1078ms)
			ProcessInitializeOnLoadMethodAttributes (802ms)
			AfterProcessingInitializeOnLoad (25ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (30ms)
Refreshing native plugins compatible for Editor in 16.93 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5480 Unused Serialized files (Serialized files now loaded: 0)
Unloading 58 unused Assets / (322.3 KB). Loaded Objects now: 6159.
Memory consumption went from 189.6 MB to 189.3 MB.
Total: 6.038000 ms (FindLiveObjects: 0.562500 ms CreateObjectMapping: 0.301900 ms MarkObjects: 5.027100 ms  DeleteObjects: 0.144500 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 674.919670 seconds.
  path: Assets/Scripts/Manager/LanguageManager.cs
  artifactKey: Guid(5f31673f0da53e747b3aa667bf5bfc77) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Manager/LanguageManager.cs using Guid(5f31673f0da53e747b3aa667bf5bfc77) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '4f2bba3e7818b3cfab3c2fdae1aeb094') in 0.002128 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.440 seconds
Refreshing native plugins compatible for Editor in 31.82 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  5.903 seconds
Domain Reload Profiling: 7319ms
	BeginReloadAssembly (336ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (83ms)
	RebuildCommonClasses (61ms)
	RebuildNativeTypeToScriptingClass (18ms)
	initialDomainReloadingComplete (88ms)
	LoadAllAssembliesAndSetupDomain (910ms)
		LoadAssemblies (1063ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (39ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (20ms)
	FinalizeReload (5905ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2990ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (18ms)
			SetLoadedEditorAssemblies (10ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (194ms)
			ProcessInitializeOnLoadAttributes (1240ms)
			ProcessInitializeOnLoadMethodAttributes (1474ms)
			AfterProcessingInitializeOnLoad (54ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (52ms)
Refreshing native plugins compatible for Editor in 28.37 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5480 Unused Serialized files (Serialized files now loaded: 0)
Unloading 56 unused Assets / (321.7 KB). Loaded Objects now: 6163.
Memory consumption went from 189.7 MB to 189.3 MB.
Total: 10.486900 ms (FindLiveObjects: 1.226100 ms CreateObjectMapping: 0.549400 ms MarkObjects: 8.372000 ms  DeleteObjects: 0.338200 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  7.549 seconds
Refreshing native plugins compatible for Editor in 74.22 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  4.915 seconds
Domain Reload Profiling: 12346ms
	BeginReloadAssembly (1775ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (33ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (700ms)
	RebuildCommonClasses (402ms)
	RebuildNativeTypeToScriptingClass (339ms)
	initialDomainReloadingComplete (1073ms)
	LoadAllAssembliesAndSetupDomain (3841ms)
		LoadAssemblies (4570ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (134ms)
			TypeCache.Refresh (38ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (88ms)
	FinalizeReload (4916ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1526ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (6ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (111ms)
			ProcessInitializeOnLoadAttributes (689ms)
			ProcessInitializeOnLoadMethodAttributes (688ms)
			AfterProcessingInitializeOnLoad (22ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (28ms)
Refreshing native plugins compatible for Editor in 19.40 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5493 Unused Serialized files (Serialized files now loaded: 0)
Unloading 69 unused Assets / (1.0 MB). Loaded Objects now: 6167.
Memory consumption went from 193.7 MB to 192.7 MB.
Total: 8.767300 ms (FindLiveObjects: 2.986100 ms CreateObjectMapping: 0.730700 ms MarkObjects: 4.424000 ms  DeleteObjects: 0.625200 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 397.830592 seconds.
  path: Assets/TextMesh Pro/Fonts/LiberationSans.ttf
  artifactKey: Guid(e3265ab4bf004d28a9537516768c1c75) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TextMesh Pro/Fonts/LiberationSans.ttf using Guid(e3265ab4bf004d28a9537516768c1c75) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '3cc557989c01b8544d1b277bf4803f35') in 0.786307 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 24
========================================================================
Received Import Request.
  Time since last request: 66.077687 seconds.
  path: Assets/TextMesh Pro/Fonts/Ailibaba SDF.asset
  artifactKey: Guid(8da56a0de8f75df488ea91a5622278bf) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TextMesh Pro/Fonts/Ailibaba SDF.asset using Guid(8da56a0de8f75df488ea91a5622278bf) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '45adb1550b59862554ed335b3f6ea57a') in 1.558643 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 7
========================================================================
Received Import Request.
  Time since last request: 17.508329 seconds.
  path: Assets/TextMesh Pro/Fonts/Ailibaba.ttf
  artifactKey: Guid(22e68cd78c4bb7e439d608b62e993549) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TextMesh Pro/Fonts/Ailibaba.ttf using Guid(22e68cd78c4bb7e439d608b62e993549) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '0ef91dbe654bdc73eb33844d77c7cda6') in 0.027402 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 5
========================================================================
Received Import Request.
  Time since last request: 127.522872 seconds.
  path: Assets/TextMesh Pro/Resources/Fonts & Materials/LiberationSans SDF.asset
  artifactKey: Guid(8f586378b4e144a9851e7b34d9b748ee) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TextMesh Pro/Resources/Fonts & Materials/LiberationSans SDF.asset using Guid(8f586378b4e144a9851e7b34d9b748ee) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: 'db5433f81a2495a06bcba64c463185f3') in 0.330841 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 11
========================================================================
Received Import Request.
  Time since last request: 0.000251 seconds.
  path: Assets/TextMesh Pro/Resources/Fonts & Materials/LiberationSans SDF - Fallback.asset
  artifactKey: Guid(2e498d1c8094910479dc3e1b768306a4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TextMesh Pro/Resources/Fonts & Materials/LiberationSans SDF - Fallback.asset using Guid(2e498d1c8094910479dc3e1b768306a4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '1dc3a3261ba27e2f9f55dcf71bae8317') in 0.089155 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 8
========================================================================
Received Import Request.
  Time since last request: 196.994220 seconds.
  path: Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Textures/Getting Started/Actions.png
  artifactKey: Guid(75226492c73547cc8ed9786065e1b820) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Textures/Getting Started/Actions.png using Guid(75226492c73547cc8ed9786065e1b820) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '57c2118bbfa1e3b667b9e171eff0b3b7') in 0.095670 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000400 seconds.
  path: Packages/com.unity.render-pipelines.core/Editor/LookDev/Icons/Add.png
  artifactKey: Guid(cbaeed03e5416aa4bae23110b63a0c7d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Packages/com.unity.render-pipelines.core/Editor/LookDev/Icons/Add.png using Guid(cbaeed03e5416aa4bae23110b63a0c7d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '18c4b613bea012aaa73ad85e6fff2a31') in 0.092507 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Icons/Add-Folder.png
  artifactKey: Guid(054c3b8adca809445a47f88bba1ca030) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Icons/Add-Folder.png using Guid(054c3b8adca809445a47f88bba1ca030) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: 'cda385d538dbc9f60e3842c238eea57b') in 0.055480 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Icons/Add-Component.png
  artifactKey: Guid(418f101e22ec47048db325bb0849cd98) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Icons/Add-Component.png using Guid(418f101e22ec47048db325bb0849cd98) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '7b90a4cf207a5d6e9f51976a4db7ff1c') in 0.050073 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000063 seconds.
  path: Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Textures/Getting Started/Add Component by Shortcut.png
  artifactKey: Guid(64d2fb1415084b6598270edd2931877f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Textures/Getting Started/Add Component by Shortcut.png using Guid(64d2fb1415084b6598270edd2931877f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '6c6d2f67e3e0827f4220545b6832df6d') in 0.049655 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000236 seconds.
  path: Packages/com.unity.render-pipelines.core/Editor/LookDev/Icons/<EMAIL>
  artifactKey: Guid(79aba0b290c471c4ba3d3f818d4e9b15) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Packages/com.unity.render-pipelines.core/Editor/LookDev/Icons/<EMAIL> using Guid(79aba0b290c471c4ba3d3f818d4e9b15) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: 'f696c0ee1c826cdbb203770088ebc633') in 0.045655 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000066 seconds.
  path: Packages/com.unity.render-pipelines.core/Editor/Lighting/Icons/Advanced_Pressed_mini.png
  artifactKey: Guid(c1d870384c7ed3c49b30d01278bf3949) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Packages/com.unity.render-pipelines.core/Editor/Lighting/Icons/Advanced_Pressed_mini.png using Guid(c1d870384c7ed3c49b30d01278bf3949) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '613b480b1edd53a952d02de5e66a1243') in 0.077376 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.723 seconds
Refreshing native plugins compatible for Editor in 35.33 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  8.732 seconds
Domain Reload Profiling: 11258ms
	BeginReloadAssembly (722ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (52ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (186ms)
	RebuildCommonClasses (132ms)
	RebuildNativeTypeToScriptingClass (33ms)
	initialDomainReloadingComplete (151ms)
	LoadAllAssembliesAndSetupDomain (1488ms)
		LoadAssemblies (1797ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (54ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (34ms)
	FinalizeReload (8732ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (3818ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (23ms)
			SetLoadedEditorAssemblies (13ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (250ms)
			ProcessInitializeOnLoadAttributes (2056ms)
			ProcessInitializeOnLoadMethodAttributes (1429ms)
			AfterProcessingInitializeOnLoad (47ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (121ms)
Refreshing native plugins compatible for Editor in 21.96 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5493 Unused Serialized files (Serialized files now loaded: 0)
Unloading 67 unused Assets / (0.9 MB). Loaded Objects now: 6264.
Memory consumption went from 198.7 MB to 197.7 MB.
Total: 7.562900 ms (FindLiveObjects: 0.571200 ms CreateObjectMapping: 0.333300 ms MarkObjects: 6.088600 ms  DeleteObjects: 0.568200 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 553.180899 seconds.
  path: Assets/streamingAssets/New Folder
  artifactKey: Guid(0d3a0c9c121da804da6c48094706ad84) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/streamingAssets/New Folder using Guid(0d3a0c9c121da804da6c48094706ad84) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: 'ecacd7cfb2354d6f44399aa5972edb8a') in 0.007148 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 8.659890 seconds.
  path: Assets/streamingAssets/Save
  artifactKey: Guid(0d3a0c9c121da804da6c48094706ad84) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/streamingAssets/Save using Guid(0d3a0c9c121da804da6c48094706ad84) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '284c35ea239c30e0b7e73ab940d7838a') in 0.000860 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 56.338520 seconds.
  path: Assets/Scripts/Manager/ConfigurationManager.cs
  artifactKey: Guid(3b38edecd952e094a9fed64269c1eeae) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Manager/ConfigurationManager.cs using Guid(3b38edecd952e094a9fed64269c1eeae) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '627eaf9bd38f654d3e7e192d58b89165') in 0.001459 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 4.655325 seconds.
  path: Assets/Scripts/Manager/AudioManager.cs
  artifactKey: Guid(9fac522209a037f439d5c36644915d13) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Manager/AudioManager.cs using Guid(9fac522209a037f439d5c36644915d13) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '031b2bfadf7a4ac03d6200a577b5eadb') in 0.003153 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.222 seconds
Refreshing native plugins compatible for Editor in 21.07 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in 17.870 seconds
Domain Reload Profiling: 19078ms
	BeginReloadAssembly (270ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (61ms)
	RebuildCommonClasses (45ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (67ms)
	LoadAllAssembliesAndSetupDomain (809ms)
		LoadAssemblies (869ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (87ms)
			TypeCache.Refresh (24ms)
				TypeCache.ScanAssembly (4ms)
			ScanForSourceGeneratedMonoScriptInfo (40ms)
			ResolveRequiredComponents (20ms)
	FinalizeReload (17871ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (3143ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (17ms)
			SetLoadedEditorAssemblies (8ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (168ms)
			ProcessInitializeOnLoadAttributes (1433ms)
			ProcessInitializeOnLoadMethodAttributes (1486ms)
			AfterProcessingInitializeOnLoad (31ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (40ms)
Refreshing native plugins compatible for Editor in 23.20 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5492 Unused Serialized files (Serialized files now loaded: 0)
Unloading 67 unused Assets / (0.9 MB). Loaded Objects now: 6269.
Memory consumption went from 198.2 MB to 197.2 MB.
Total: 11.260500 ms (FindLiveObjects: 4.353900 ms CreateObjectMapping: 0.569000 ms MarkObjects: 5.640600 ms  DeleteObjects: 0.695200 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 36.377789 seconds.
  path: Assets/Scripts/Manager/ConfigurationManager.cs
  artifactKey: Guid(3b38edecd952e094a9fed64269c1eeae) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Manager/ConfigurationManager.cs using Guid(3b38edecd952e094a9fed64269c1eeae) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: 'acbe3ceffe6682c4478dcd732ecf9fff') in 0.003833 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.894 seconds
Refreshing native plugins compatible for Editor in 56.00 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  8.880 seconds
Domain Reload Profiling: 10756ms
	BeginReloadAssembly (345ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (66ms)
	RebuildCommonClasses (69ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (113ms)
	LoadAllAssembliesAndSetupDomain (1331ms)
		LoadAssemblies (1368ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (172ms)
			TypeCache.Refresh (40ms)
				TypeCache.ScanAssembly (14ms)
			ScanForSourceGeneratedMonoScriptInfo (89ms)
			ResolveRequiredComponents (38ms)
	FinalizeReload (8881ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2999ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (62ms)
			SetLoadedEditorAssemblies (14ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (417ms)
			ProcessInitializeOnLoadAttributes (1337ms)
			ProcessInitializeOnLoadMethodAttributes (1139ms)
			AfterProcessingInitializeOnLoad (30ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (44ms)
Refreshing native plugins compatible for Editor in 24.67 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5492 Unused Serialized files (Serialized files now loaded: 0)
Unloading 67 unused Assets / (0.9 MB). Loaded Objects now: 6273.
Memory consumption went from 198.2 MB to 197.2 MB.
Total: 10.205600 ms (FindLiveObjects: 0.634100 ms CreateObjectMapping: 1.629900 ms MarkObjects: 6.867200 ms  DeleteObjects: 1.072800 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 165.874906 seconds.
  path: Assets/Scripts/Manager/ConfigurationManager.cs
  artifactKey: Guid(3b38edecd952e094a9fed64269c1eeae) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Manager/ConfigurationManager.cs using Guid(3b38edecd952e094a9fed64269c1eeae) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '00cbca50449fbd34c318eee3d4edc442') in 0.004599 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 499.098420 seconds.
  path: Assets/Scripts/BootSetting.cs
  artifactKey: Guid(9ba7b49fefb0eb64c8234b2ccbcb75c9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/BootSetting.cs using Guid(9ba7b49fefb0eb64c8234b2ccbcb75c9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '9650612cc5e45bb2420b37d01ddb5818') in 0.003566 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 10.539537 seconds.
  path: Assets/Scripts/Manager
  artifactKey: Guid(af5c399b755255b4f9b4855d2b2b2ff8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Manager using Guid(af5c399b755255b4f9b4855d2b2b2ff8) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '85caca370edb99850fb633292b8647db') in 0.000939 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 29.188718 seconds.
  path: Assets/Scripts/Manager/New Folder
  artifactKey: Guid(b55f3293db8d1e049ac750ac2a147bdd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Manager/New Folder using Guid(b55f3293db8d1e049ac750ac2a147bdd) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: 'ac346d9a4e2253eb4fc0b6606e921e83') in 0.001080 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 7.536517 seconds.
  path: Assets/Scripts/Manager/Base
  artifactKey: Guid(b55f3293db8d1e049ac750ac2a147bdd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Manager/Base using Guid(b55f3293db8d1e049ac750ac2a147bdd) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '24df39393ce9206222939be8d330c543') in 0.001130 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 79.889621 seconds.
  path: Assets/Scripts/Manager/LanguageManager.cs
  artifactKey: Guid(5f31673f0da53e747b3aa667bf5bfc77) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Manager/LanguageManager.cs using Guid(5f31673f0da53e747b3aa667bf5bfc77) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '5992605f4d4a2796f28455717694186b') in 0.001110 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 2628.845274 seconds.
  path: Assets/Scripts/DialogueTree/DialoguePlayer.cs
  artifactKey: Guid(7d7bfbdcd0f845a4eadaa04239d62b50) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/DialogueTree/DialoguePlayer.cs using Guid(7d7bfbdcd0f845a4eadaa04239d62b50) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '801e2d3e937fecf5de4458f309908685') in 0.001424 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 94.878002 seconds.
  path: Assets/Resources/Data/DefaultConfiguration.json
  artifactKey: Guid(08a885401fac10d4889c121f75551068) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/DefaultConfiguration.json using Guid(08a885401fac10d4889c121f75551068) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '4100e481c5af32fccb31b0b14d4da71a') in 0.020179 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 1857.303896 seconds.
  path: Assets/Scripts/Test/ConfigurationManagerTest.cs
  artifactKey: Guid(5fcd754e94d29ba46b73356ff6193b9d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Test/ConfigurationManagerTest.cs using Guid(5fcd754e94d29ba46b73356ff6193b9d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '2d91b0ed5b90d6cee794d43267b7d662') in 0.002125 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 1.799860 seconds.
  path: Assets/Scripts/Test/ConfigurationUsageExample.cs
  artifactKey: Guid(a76e5f97b595bb544b2d3494e96ac91f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Test/ConfigurationUsageExample.cs using Guid(a76e5f97b595bb544b2d3494e96ac91f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: 'f0089c8ef82c953d5cdebbbeeb1cb5e0') in 0.001922 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.820 seconds
Refreshing native plugins compatible for Editor in 20.01 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in 21.713 seconds
Domain Reload Profiling: 24514ms
	BeginReloadAssembly (617ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (22ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (251ms)
	RebuildCommonClasses (131ms)
	RebuildNativeTypeToScriptingClass (18ms)
	initialDomainReloadingComplete (222ms)
	LoadAllAssembliesAndSetupDomain (1811ms)
		LoadAssemblies (1886ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (114ms)
			TypeCache.Refresh (32ms)
				TypeCache.ScanAssembly (4ms)
			ScanForSourceGeneratedMonoScriptInfo (55ms)
			ResolveRequiredComponents (22ms)
	FinalizeReload (21714ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1983ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (13ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (134ms)
			ProcessInitializeOnLoadAttributes (970ms)
			ProcessInitializeOnLoadMethodAttributes (817ms)
			AfterProcessingInitializeOnLoad (39ms)
			EditorAssembliesLoaded (5ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (43ms)
Refreshing native plugins compatible for Editor in 17.77 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5492 Unused Serialized files (Serialized files now loaded: 0)
Unloading 69 unused Assets / (0.9 MB). Loaded Objects now: 6277.
Memory consumption went from 198.2 MB to 197.2 MB.
Total: 10.089600 ms (FindLiveObjects: 0.592600 ms CreateObjectMapping: 0.412100 ms MarkObjects: 8.127800 ms  DeleteObjects: 0.955300 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.380 seconds
Refreshing native plugins compatible for Editor in 17.17 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  3.966 seconds
Domain Reload Profiling: 5331ms
	BeginReloadAssembly (251ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (63ms)
	RebuildCommonClasses (45ms)
	RebuildNativeTypeToScriptingClass (15ms)
	initialDomainReloadingComplete (62ms)
	LoadAllAssembliesAndSetupDomain (992ms)
		LoadAssemblies (1096ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (27ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (3966ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2014ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (6ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (142ms)
			ProcessInitializeOnLoadAttributes (899ms)
			ProcessInitializeOnLoadMethodAttributes (933ms)
			AfterProcessingInitializeOnLoad (26ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (31ms)
Refreshing native plugins compatible for Editor in 16.82 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5492 Unused Serialized files (Serialized files now loaded: 0)
Unloading 67 unused Assets / (0.9 MB). Loaded Objects now: 6281.
Memory consumption went from 198.2 MB to 197.3 MB.
Total: 6.296600 ms (FindLiveObjects: 0.774800 ms CreateObjectMapping: 0.438600 ms MarkObjects: 4.489500 ms  DeleteObjects: 0.592700 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 214.601467 seconds.
  path: Assets/Scripts/Manager/EventCenter.cs
  artifactKey: Guid(70838521646eaa7428a7d0adc44b9417) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Manager/EventCenter.cs using Guid(70838521646eaa7428a7d0adc44b9417) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '1912827ee86c3d2162a032715a51faa4') in 0.003260 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 8.798517 seconds.
  path: Assets/Scripts/Test/ConfigurationManagerTest.cs
  artifactKey: Guid(5fcd754e94d29ba46b73356ff6193b9d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Test/ConfigurationManagerTest.cs using Guid(5fcd754e94d29ba46b73356ff6193b9d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: 'dac9fa1eed7389f953c01b2632f51607') in 0.001231 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.755 seconds
Refreshing native plugins compatible for Editor in 81.08 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  6.945 seconds
Domain Reload Profiling: 8676ms
	BeginReloadAssembly (296ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (68ms)
	RebuildCommonClasses (59ms)
	RebuildNativeTypeToScriptingClass (36ms)
	initialDomainReloadingComplete (244ms)
	LoadAllAssembliesAndSetupDomain (1096ms)
		LoadAssemblies (1089ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (155ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (135ms)
	FinalizeReload (6946ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2567ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (16ms)
			SetLoadedEditorAssemblies (9ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (178ms)
			ProcessInitializeOnLoadAttributes (1164ms)
			ProcessInitializeOnLoadMethodAttributes (1141ms)
			AfterProcessingInitializeOnLoad (58ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (47ms)
Refreshing native plugins compatible for Editor in 26.49 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5492 Unused Serialized files (Serialized files now loaded: 0)
Unloading 67 unused Assets / (0.9 MB). Loaded Objects now: 6285.
Memory consumption went from 198.2 MB to 197.3 MB.
Total: 11.636100 ms (FindLiveObjects: 0.740900 ms CreateObjectMapping: 0.515200 ms MarkObjects: 8.635800 ms  DeleteObjects: 1.742000 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 296.232332 seconds.
  path: Assets/streamingAssets/DefaultConfiguration.json
  artifactKey: Guid(c5b4129efca23a3468b6f7734602f903) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/streamingAssets/DefaultConfiguration.json using Guid(c5b4129efca23a3468b6f7734602f903) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '9c62ebca4acaaf0f1ec6961381e03910') in 0.042362 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 72.531986 seconds.
  path: Assets/streamingAssets/DefaultConfiguration.json
  artifactKey: Guid(c5b4129efca23a3468b6f7734602f903) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/streamingAssets/DefaultConfiguration.json using Guid(c5b4129efca23a3468b6f7734602f903) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '23eeb74f3e4a44fa0ee4e839d822bc57') in 2.049654 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.673 seconds
Refreshing native plugins compatible for Editor in 67.40 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  3.599 seconds
Domain Reload Profiling: 5253ms
	BeginReloadAssembly (379ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (90ms)
	RebuildCommonClasses (54ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (93ms)
	LoadAllAssembliesAndSetupDomain (1110ms)
		LoadAssemblies (1242ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (49ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (35ms)
	FinalizeReload (3601ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1547ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (123ms)
			ProcessInitializeOnLoadAttributes (683ms)
			ProcessInitializeOnLoadMethodAttributes (703ms)
			AfterProcessingInitializeOnLoad (23ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (34ms)
Refreshing native plugins compatible for Editor in 18.54 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5492 Unused Serialized files (Serialized files now loaded: 0)
Unloading 67 unused Assets / (0.9 MB). Loaded Objects now: 6289.
Memory consumption went from 198.2 MB to 197.3 MB.
Total: 5.960000 ms (FindLiveObjects: 0.566000 ms CreateObjectMapping: 0.348500 ms MarkObjects: 4.507800 ms  DeleteObjects: 0.536200 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.468 seconds
Refreshing native plugins compatible for Editor in 59.28 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  5.059 seconds
Domain Reload Profiling: 7508ms
	BeginReloadAssembly (479ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (104ms)
	RebuildCommonClasses (59ms)
	RebuildNativeTypeToScriptingClass (26ms)
	initialDomainReloadingComplete (109ms)
	LoadAllAssembliesAndSetupDomain (1775ms)
		LoadAssemblies (1693ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (351ms)
			TypeCache.Refresh (25ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (3ms)
			ResolveRequiredComponents (314ms)
	FinalizeReload (5059ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1966ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (6ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (123ms)
			ProcessInitializeOnLoadAttributes (878ms)
			ProcessInitializeOnLoadMethodAttributes (923ms)
			AfterProcessingInitializeOnLoad (26ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (29ms)
Refreshing native plugins compatible for Editor in 44.41 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5492 Unused Serialized files (Serialized files now loaded: 0)
Unloading 67 unused Assets / (0.9 MB). Loaded Objects now: 6293.
Memory consumption went from 198.2 MB to 197.3 MB.
Total: 15.165900 ms (FindLiveObjects: 0.734400 ms CreateObjectMapping: 0.682300 ms MarkObjects: 13.125400 ms  DeleteObjects: 0.621600 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.081 seconds
Refreshing native plugins compatible for Editor in 16.52 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  3.870 seconds
Domain Reload Profiling: 4936ms
	BeginReloadAssembly (333ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (74ms)
	RebuildCommonClasses (57ms)
	RebuildNativeTypeToScriptingClass (23ms)
	initialDomainReloadingComplete (78ms)
	LoadAllAssembliesAndSetupDomain (574ms)
		LoadAssemblies (711ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (62ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (4ms)
			ScanForSourceGeneratedMonoScriptInfo (30ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (3871ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1682ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (14ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (143ms)
			ProcessInitializeOnLoadAttributes (767ms)
			ProcessInitializeOnLoadMethodAttributes (730ms)
			AfterProcessingInitializeOnLoad (24ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (27ms)
Refreshing native plugins compatible for Editor in 16.85 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5490 Unused Serialized files (Serialized files now loaded: 0)
Unloading 69 unused Assets / (0.9 MB). Loaded Objects now: 6295.
Memory consumption went from 198.2 MB to 197.3 MB.
Total: 7.505600 ms (FindLiveObjects: 0.709400 ms CreateObjectMapping: 0.587200 ms MarkObjects: 5.609200 ms  DeleteObjects: 0.598500 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.473 seconds
Refreshing native plugins compatible for Editor in 20.74 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  3.299 seconds
Domain Reload Profiling: 4750ms
	BeginReloadAssembly (408ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (85ms)
	RebuildCommonClasses (69ms)
	RebuildNativeTypeToScriptingClass (23ms)
	initialDomainReloadingComplete (128ms)
	LoadAllAssembliesAndSetupDomain (824ms)
		LoadAssemblies (1006ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (79ms)
			TypeCache.Refresh (22ms)
				TypeCache.ScanAssembly (6ms)
			ScanForSourceGeneratedMonoScriptInfo (35ms)
			ResolveRequiredComponents (17ms)
	FinalizeReload (3299ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1567ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (10ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (116ms)
			ProcessInitializeOnLoadAttributes (707ms)
			ProcessInitializeOnLoadMethodAttributes (701ms)
			AfterProcessingInitializeOnLoad (28ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (28ms)
Refreshing native plugins compatible for Editor in 16.79 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5490 Unused Serialized files (Serialized files now loaded: 0)
Unloading 67 unused Assets / (0.9 MB). Loaded Objects now: 6299.
Memory consumption went from 198.2 MB to 197.3 MB.
Total: 12.244800 ms (FindLiveObjects: 0.732800 ms CreateObjectMapping: 0.530400 ms MarkObjects: 10.439200 ms  DeleteObjects: 0.540700 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.809 seconds
Refreshing native plugins compatible for Editor in 21.81 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  5.894 seconds
Domain Reload Profiling: 7682ms
	BeginReloadAssembly (386ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (115ms)
	RebuildCommonClasses (100ms)
	RebuildNativeTypeToScriptingClass (39ms)
	initialDomainReloadingComplete (90ms)
	LoadAllAssembliesAndSetupDomain (1172ms)
		LoadAssemblies (1287ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (78ms)
			TypeCache.Refresh (18ms)
				TypeCache.ScanAssembly (4ms)
			ScanForSourceGeneratedMonoScriptInfo (37ms)
			ResolveRequiredComponents (19ms)
	FinalizeReload (5895ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2412ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (6ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (149ms)
			ProcessInitializeOnLoadAttributes (1117ms)
			ProcessInitializeOnLoadMethodAttributes (1099ms)
			AfterProcessingInitializeOnLoad (29ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (61ms)
Refreshing native plugins compatible for Editor in 18.39 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5490 Unused Serialized files (Serialized files now loaded: 0)
Unloading 67 unused Assets / (0.9 MB). Loaded Objects now: 6303.
Memory consumption went from 198.2 MB to 197.3 MB.
Total: 7.582600 ms (FindLiveObjects: 0.533900 ms CreateObjectMapping: 0.300000 ms MarkObjects: 6.198500 ms  DeleteObjects: 0.549000 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.395 seconds
Refreshing native plugins compatible for Editor in 40.23 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  5.673 seconds
Domain Reload Profiling: 8048ms
	BeginReloadAssembly (618ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (178ms)
	RebuildCommonClasses (96ms)
	RebuildNativeTypeToScriptingClass (28ms)
	initialDomainReloadingComplete (104ms)
	LoadAllAssembliesAndSetupDomain (1529ms)
		LoadAssemblies (1836ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (46ms)
			TypeCache.Refresh (13ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (25ms)
	FinalizeReload (5674ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2576ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (136ms)
			SetLoadedEditorAssemblies (34ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (157ms)
			ProcessInitializeOnLoadAttributes (1116ms)
			ProcessInitializeOnLoadMethodAttributes (1074ms)
			AfterProcessingInitializeOnLoad (59ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (39ms)
Refreshing native plugins compatible for Editor in 22.39 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5490 Unused Serialized files (Serialized files now loaded: 0)
Unloading 67 unused Assets / (0.9 MB). Loaded Objects now: 6307.
Memory consumption went from 198.2 MB to 197.3 MB.
Total: 6.875900 ms (FindLiveObjects: 0.610400 ms CreateObjectMapping: 0.332400 ms MarkObjects: 4.643200 ms  DeleteObjects: 1.288500 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
