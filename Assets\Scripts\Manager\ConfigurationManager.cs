using UnityEngine;
using System.IO;
using Newtonsoft.Json.Linq;

public class ConfigurationManager : SingletonMono<ConfigurationManager> //配置管理器
{
    private static JObject _configData;
    private static readonly string CONFIG_FILE_NAME = "Configuration.json";
    private static readonly string DEFAULT_CONFIG_FILE_NAME = "DefaultConfiguration.json";

    // 配置数据，其他脚本可以直接访问
    public static JObject ConfigData
    {
        get
        {
            if (_configData == null)
            {
                Debug.LogWarning("[ConfigurationManager] 配置数据尚未加载。Awake应该处理这个问题。正在返回空对象。");
                // 为防止空引用，返回一个空对象，但这表示可能存在问题。
                _configData = new JObject();
            }
            return _configData;
        }
    }

    // 使用Awake进行初始化，以符合Unity对MonoBehaviour的要求。
    private void Awake()
    {
        LoadConfiguration();
    }

    // 检查配置文件是否存在，如果不存在则创建
    private void CheckAndCreateConfigFile()
    {
        string persistentConfigPath = Path.Combine(Application.persistentDataPath, CONFIG_FILE_NAME);

        if (!File.Exists(persistentConfigPath))
        {
            string streamingConfigPath = Path.Combine(Application.streamingAssetsPath, DEFAULT_CONFIG_FILE_NAME);

            if (File.Exists(streamingConfigPath))
            {
                try
                {
                    string defaultConfigContent = File.ReadAllText(streamingConfigPath);
                    File.WriteAllText(persistentConfigPath, defaultConfigContent);
                    Debug.Log($"[ConfigurationManager] 已从默认配置创建新的配置文件: {persistentConfigPath}");
                }
                catch (System.Exception e)
                {
                    Debug.LogError($"[ConfigurationManager] 创建配置文件失败: {e.Message}");
                }
            }
            else
            {
                Debug.LogError($"[ConfigurationManager] 默认配置文件不存在: {streamingConfigPath}");
            }
        }
    }

    // 加载配置文件到内存
    private void LoadConfiguration()
    {
        CheckAndCreateConfigFile();

        string configPath = Path.Combine(Application.persistentDataPath, CONFIG_FILE_NAME);

        if (File.Exists(configPath))
        {
            try
            {
                string configContent = File.ReadAllText(configPath);
                _configData = JObject.Parse(configContent);
                Debug.Log($"[ConfigurationManager] 配置文件加载成功: {configPath}");
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[ConfigurationManager] 配置文件加载失败: {e.Message}");
                _configData = new JObject();
            }
        }
        else
        {
            Debug.LogWarning("[ConfigurationManager] 配置文件不存在，创建空配置");
            _configData = new JObject();
        }
    }

    // 保存配置到文件
    public static void SaveConfiguration()
    {
        if (_configData == null)
        {
            Debug.LogWarning("[ConfigurationManager] 配置数据为空，无法保存");
            return;
        }

        string configPath = Path.Combine(Application.persistentDataPath, CONFIG_FILE_NAME);

        try
        {
            string configContent = _configData.ToString();
            File.WriteAllText(configPath, configContent);
            Debug.Log($"[ConfigurationManager] 配置文件保存成功: {configPath}");
        }
        catch (System.Exception e)
        {
            Debug.LogError($"[ConfigurationManager] 配置文件保存失败: {e.Message}");
        }
    }

    // 获取配置值的泛型方法
    public static T GetValue<T>(string path, T defaultValue = default)
    {
        try
        {
            JToken token = ConfigData.SelectToken(path);
            if (token != null)
            {
                return token.ToObject<T>();
            }
        }
        catch (System.Exception e)
        {
            Debug.LogWarning($"[ConfigurationManager] 获取配置值失败 {path}: {e.Message}");
        }

        return defaultValue;
    }

    // 设置配置值的泛型方法
    public static void SetValue<T>(string path, T value)
    {
        try
        {
            // 使用JsonPath设置值，支持嵌套路径
            string[] pathParts = path.Split('.');
            JObject current = ConfigData;

            // 遍历路径，确保所有父级对象都存在
            for (int i = 0; i < pathParts.Length - 1; i++)
            {
                if (current[pathParts[i]] == null)
                {
                    current[pathParts[i]] = new JObject();
                }
                current = (JObject)current[pathParts[i]];
            }

            // 设置最终值
            current[pathParts[pathParts.Length - 1]] = JToken.FromObject(value);
            Debug.Log($"[ConfigurationManager] 设置配置值: {path} = {value}");
        }
        catch (System.Exception e)
        {
            Debug.LogError($"[ConfigurationManager] 设置配置值失败 {path}: {e.Message}");
        }
    }

    // 检查配置路径是否存在
    public static bool HasValue(string path)
    {
        try
        {
            JToken token = ConfigData.SelectToken(path);
            return token != null;
        }
        catch (System.Exception e)
        {
            Debug.LogWarning($"[ConfigurationManager] 检查配置路径失败 {path}: {e.Message}");
            return false;
        }
    }

    // 删除配置值
    public static void RemoveValue(string path)
    {
        try
        {
            JToken token = ConfigData.SelectToken(path);
            if (token != null)
            {
                token.Remove();
                Debug.Log($"[ConfigurationManager] 删除配置值: {path}");
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"[ConfigurationManager] 删除配置值失败 {path}: {e.Message}");
        }
    }

    // 重新加载配置文件
    public static void ReloadConfiguration()
    {
        Instance.LoadConfiguration();
        Debug.Log("[ConfigurationManager] 配置文件已重新加载");
    }

    // 获取配置文件的完整路径
    public static string GetConfigFilePath()
    {
        return Path.Combine(Application.persistentDataPath, CONFIG_FILE_NAME);
    }

    // 获取默认配置文件的完整路径
    public static string GetDefaultConfigFilePath()
    {
        return Path.Combine(Application.streamingAssetsPath, DEFAULT_CONFIG_FILE_NAME);
    }
}
