using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.IO;
using System.Reflection;

[CustomPropertyDrawer(typeof(NodeSequence.DialogueList))]
[CustomPropertyDrawer(typeof(NodeShout.DialogueList))]
[CustomPropertyDrawer(typeof(NodeBranching.DialogueList))]
public class EditorTextConversion : PropertyDrawer
{
    private Dictionary<string, string> textDictionary;

    public EditorTextConversion()
    {
        // 初始化字典并读取TSV文件
        textDictionary = new Dictionary<string, string>();
        string filePath = Path.Combine(Application.streamingAssetsPath, "language/简体中文.tsv");
        if (File.Exists(filePath))
        {
            string[] lines = File.ReadAllLines(filePath);
            foreach (string line in lines)
            {
                string[] parts = line.Split('\t');
                if (parts.Length >= 4)
                {
                    textDictionary[parts[1]] = parts[3];
                }
            }
        }
    }

    public override void OnGUI(Rect position, SerializedProperty property, GUIContent label)
    {
        EditorGUI.BeginProperty(position, label, property);

        // 使用唯一的key来存储每个元素的折叠状态
        string foldoutKey = property.propertyPath + ".foldout";
        bool foldout = EditorPrefs.GetBool(foldoutKey, true);
        foldout = EditorGUI.Foldout(new Rect(position.x, position.y, position.width, EditorGUIUtility.singleLineHeight), foldout, label);
        EditorPrefs.SetBool(foldoutKey, foldout);

        if (foldout)
        {
            // 获取所有属性
            SerializedProperty iterator = property.Copy();
            SerializedProperty endProperty = iterator.GetEndProperty();

            // 绘制所有属性
            position.y += EditorGUIUtility.singleLineHeight;
            while (iterator.NextVisible(true) && !SerializedProperty.EqualContents(iterator, endProperty))
            {
                if (iterator.name == "textID")
                {
                    // 绘制 textID 字段
                    EditorGUI.PropertyField(new Rect(position.x, position.y, position.width, EditorGUIUtility.singleLineHeight), iterator);
                    position.y += EditorGUIUtility.singleLineHeight;

                    // 根据 textID 更新 textContent
                    SerializedProperty textContentProp = property.FindPropertyRelative("textContent");
                    if (textDictionary.TryGetValue(iterator.stringValue, out string textContent))
                    {
                        textContentProp.stringValue = textContent;
                    }
                    else if (!string.IsNullOrEmpty(iterator.stringValue))
                    {
                        // 如果textID无效且不为空，清空textContent
                        if (!string.IsNullOrEmpty(textContentProp.stringValue))
                        {
                            textContentProp.stringValue = "";
                            Debug.Log($"无效的textID '{iterator.stringValue}' 对应的textContent已被清空");
                        }
                    }
                }
                else if (iterator.name == "textContent")
                {
                    // 绘制 textContent 字段（只读）
                    EditorGUI.LabelField(new Rect(position.x, position.y, position.width, EditorGUIUtility.singleLineHeight), "Text Content", iterator.stringValue);
                    position.y += EditorGUIUtility.singleLineHeight;
                }
                else
                {
                    // 绘制其他属性
                    EditorGUI.PropertyField(new Rect(position.x, position.y, position.width, EditorGUIUtility.singleLineHeight), iterator, true);
                    position.y += EditorGUI.GetPropertyHeight(iterator, true);
                }
            }
        }

        EditorGUI.EndProperty();
    }

    public override float GetPropertyHeight(SerializedProperty property, GUIContent label)
    {
        string foldoutKey = property.propertyPath + ".foldout";
        bool foldout = EditorPrefs.GetBool(foldoutKey, true);
        if (foldout)
        {
            // 计算所有属性的总高度
            float totalHeight = EditorGUIUtility.singleLineHeight;
            SerializedProperty iterator = property.Copy();
            SerializedProperty endProperty = iterator.GetEndProperty();
            while (iterator.NextVisible(true) && !SerializedProperty.EqualContents(iterator, endProperty))
            {
                totalHeight += EditorGUI.GetPropertyHeight(iterator, true);
            }
            return totalHeight;
        }
        else
        {
            return EditorGUIUtility.singleLineHeight;
        }
    }

    /// <summary>
    /// 静态方法：验证指定对话树中所有节点的文本ID
    /// </summary>
    /// <param name="dialogueTree">要验证的对话树</param>
    public static void ValidateAllTextIDsInTree(DialogueTreeSO dialogueTree)
    {
        if (dialogueTree == null) return;

        // 加载文本字典
        Dictionary<string, string> textDictionary = LoadTextDictionary();
        if (textDictionary == null) return;

        bool hasChanges = false;
        int validatedCount = 0;
        int clearedCount = 0;

        foreach (DialogueNodeSO node in dialogueTree.allNodes)
        {
            var result = ValidateNodeTextIDs(node, textDictionary);
            if (result.hasChanges)
            {
                hasChanges = true;
            }
            validatedCount += result.validatedCount;
            clearedCount += result.clearedCount;
        }

        if (hasChanges)
        {
            EditorUtility.SetDirty(dialogueTree);
            AssetDatabase.SaveAssets();
        }

        Debug.Log($"对话树 '{dialogueTree.name}' 文本ID验证完成：" +
                  $"验证了 {validatedCount} 个文本ID，清空了 {clearedCount} 个无效的textContent");
    }

    /// <summary>
    /// 加载文本字典
    /// </summary>
    private static Dictionary<string, string> LoadTextDictionary()
    {
        Dictionary<string, string> textDictionary = new Dictionary<string, string>();
        string filePath = Path.Combine(Application.streamingAssetsPath, "language/简体中文.tsv");

        if (!File.Exists(filePath))
        {
            Debug.LogWarning($"文本字典文件不存在：{filePath}");
            return null;
        }

        try
        {
            string[] lines = File.ReadAllLines(filePath);
            foreach (string line in lines)
            {
                string[] parts = line.Split('\t');
                if (parts.Length >= 4 && !string.IsNullOrEmpty(parts[1]))
                {
                    textDictionary[parts[1]] = parts[3]; // parts[1]是textID，parts[3]是content
                }
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"加载文本字典时出错：{e.Message}");
            return null;
        }

        return textDictionary;
    }

    /// <summary>
    /// 验证单个节点的文本ID
    /// </summary>
    private static (bool hasChanges, int validatedCount, int clearedCount) ValidateNodeTextIDs(DialogueNodeSO node, Dictionary<string, string> textDictionary)
    {
        bool hasChanges = false;
        int validatedCount = 0;
        int clearedCount = 0;

        // 使用反射获取dialogueList字段
        FieldInfo dialogueListField = node.GetType().GetField("dialogueList");
        if (dialogueListField != null)
        {
            if (dialogueListField.GetValue(node) is System.Array dialogueList)
            {
                for (int i = 0; i < dialogueList.Length; i++)
                {
                    var dialogue = dialogueList.GetValue(i);
                    if (dialogue != null)
                    {
                        // 获取textID和textContent字段
                        FieldInfo textIDField = dialogue.GetType().GetField("textID");
                        FieldInfo textContentField = dialogue.GetType().GetField("textContent");

                        if (textIDField != null && textContentField != null)
                        {
                            string textID = textIDField.GetValue(dialogue) as string;
                            string currentTextContent = textContentField.GetValue(dialogue) as string;

                            if (!string.IsNullOrEmpty(textID))
                            {
                                validatedCount++;

                                // 如果textID无效且textContent不为空，则清空textContent
                                if (!textDictionary.ContainsKey(textID) && !string.IsNullOrEmpty(currentTextContent))
                                {
                                    textContentField.SetValue(dialogue, "");
                                    hasChanges = true;
                                    clearedCount++;
                                    Debug.Log($"节点 '{node.title}' 中的无效textID '{textID}' 对应的textContent已被清空");
                                }
                            }
                        }
                    }
                }
            }
        }

        return (hasChanges, validatedCount, clearedCount);
    }
}
