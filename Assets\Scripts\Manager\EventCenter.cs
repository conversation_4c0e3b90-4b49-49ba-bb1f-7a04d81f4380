using System.Collections.Generic;
using UnityEngine.Events;
using System;

public interface IEventInfo { }

public class EventInfo : IEventInfo
{
    public UnityAction method;
    public EventInfo(UnityAction action)
    {
        method += action;
    }
}

public class EventInfo<T> : IEventInfo
{
    public UnityAction<T> method;

    public EventInfo(UnityAction<T> action)
    {
        method += action;
    }
}

public class EventCenter : SingletonBase<EventCenter> // 事件管理中心
{
    private Dictionary<string, IEventInfo> _eventData = new Dictionary<string, IEventInfo>();

    #region 无参方法

    //无参注册方法：EventCenter.Instance.AddEvent("事件名称", 方法名称);
    //无参注销方法：EventCenter.Instance.RemoveEvent("事件名称", 方法名称);
    //无参通知方法：EventCenter.Instance.SendEvent("事件名称");

    public void AddEvent(string name, UnityAction action)
    {
        if (_eventData.ContainsKey(name))
        {
            if (_eventData[name] is EventInfo eventInfo)
            {
                if (eventInfo.method != null)
                {
                    foreach (var existingAction in eventInfo.method.GetInvocationList())
                    {
                        if (existingAction.Target == action.Target && existingAction.Method == action.Method)
                        {
                            return;
                        }
                    }
                }
                eventInfo.method += action;
            }
        }
        else
        {
            _eventData.Add(name, new EventInfo(action));
        }
    }

    public void RemoveEvent(string name, UnityAction action)
    {
        if (_eventData.ContainsKey(name))
        {
            if (_eventData[name] is EventInfo eventInfo)
            {
                eventInfo.method -= action;
            }
        }
    }

    public void SendEvent(string name)
    {
        if (_eventData.ContainsKey(name) && _eventData[name] is EventInfo eventInfo && eventInfo.method != null)
        {
            eventInfo.method.Invoke();
        }
    }
    #endregion

    #region 有参方法

    //有参注册方法：EventCenter.Instance.AddEvent<参数类型>("事件名称", 方法名称);
    //有参注销方法：EventCenter.Instance.RemoveEvent<参数类型>("事件名称", 方法名称);
    //有参通知方法：EventCenter.Instance.SendEvent("事件名称", 参数);

    public void AddEvent<T>(string name, UnityAction<T> action)
    {
        if (_eventData.ContainsKey(name))
        {
            if (_eventData[name] is EventInfo<T> eventInfo)
            {
                if (eventInfo.method != null)
                {
                    foreach (var existingAction in eventInfo.method.GetInvocationList())
                    {
                        if (existingAction.Target == action.Target && existingAction.Method == action.Method)
                        {
                            return;
                        }
                    }
                }
                eventInfo.method += action;
            }
        }
        else
        {
            _eventData.Add(name, new EventInfo<T>(action));
        }
    }

    public void RemoveEvent<T>(string name, UnityAction<T> action)
    {
        if (_eventData.ContainsKey(name))
        {
            if (_eventData[name] is EventInfo<T> eventInfo)
            {
                eventInfo.method -= action;
            }
        }
    }

    public void SendEvent<T>(string name, T info)
    {
        if (_eventData.ContainsKey(name) && _eventData[name] is EventInfo<T> eventInfo && eventInfo.method != null)
        {
            eventInfo.method.Invoke(info);
        }
    }
    #endregion

    public void Clear() // 外部调用清空事件
    {
        _eventData.Clear();
    }

    public Dictionary<string, IEventInfo> GetEventData() //外部读取字典信息
    {
        return _eventData;
    }
}
