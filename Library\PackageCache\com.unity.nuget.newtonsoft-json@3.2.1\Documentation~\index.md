# Newtonsoft Json Unity Package

This is a Unity package for Newtonsoft Json and corresponds to Newtonsoft.Json version 13.0.2.

Documentation for this Package is provided as links to the Json.NET Documentation.

## Json.NET is a popular JSON framework for .NET

### Documentation

* [Json.NET Main Page](https://www.newtonsoft.com/json)
* [Json.NET Documentation](https://www.newtonsoft.com/json/help/html/Introduction.htm)
* [Json.NET Source Code](https://github.com/JamesNK/Newtonsoft.Json)

### Use Cases

- Flexible JSON serializer for converting between .NET objects and JSON
- LINQ to JSON for manually reading and writing JSON
- Write indented, easy-to-read JSON
- Convert JSON to and from XML
- Supports .NET Standard 2.0, .NET 2, .NET 3.5, .NET 4, .NET 4.5, Silverlight, Windows Phone and Windows 8 Store
