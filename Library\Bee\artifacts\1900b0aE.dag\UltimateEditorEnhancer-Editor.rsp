-target:library
-out:"Library/Bee/artifacts/1900b0aE.dag/UltimateEditorEnhancer-Editor.dll"
-refout:"Library/Bee/artifacts/1900b0aE.dag/UltimateEditorEnhancer-Editor.ref.dll"
-define:UNITY_2022_3_33
-define:UNITY_2022_3
-define:UNITY_2022
-define:UNITY_5_3_OR_NEWER
-define:UNITY_5_4_OR_NEWER
-define:UNITY_5_5_OR_NEWER
-define:UNITY_5_6_OR_NEWER
-define:UNITY_2017_1_OR_NEWER
-define:UNITY_2017_2_OR_NEWER
-define:UNITY_2017_3_OR_NEWER
-define:UNITY_2017_4_OR_NEWER
-define:UNITY_2018_1_OR_NEWER
-define:UNITY_2018_2_OR_NEWER
-define:UNITY_2018_3_OR_NEWER
-define:UNITY_2018_4_OR_NEWER
-define:UNITY_2019_1_OR_NEWER
-define:UNITY_2019_2_OR_NEWER
-define:UNITY_2019_3_OR_NEWER
-define:UNITY_2019_4_OR_NEWER
-define:UNITY_2020_1_OR_NEWER
-define:UNITY_2020_2_OR_NEWER
-define:UNITY_2020_3_OR_NEWER
-define:UNITY_2021_1_OR_NEWER
-define:UNITY_2021_2_OR_NEWER
-define:UNITY_2021_3_OR_NEWER
-define:UNITY_2022_1_OR_NEWER
-define:UNITY_2022_2_OR_NEWER
-define:UNITY_2022_3_OR_NEWER
-define:PLATFORM_ARCH_64
-define:UNITY_64
-define:UNITY_INCLUDE_TESTS
-define:ENABLE_AR
-define:ENABLE_AUDIO
-define:ENABLE_CACHING
-define:ENABLE_CLOTH
-define:ENABLE_EVENT_QUEUE
-define:ENABLE_MICROPHONE
-define:ENABLE_MULTIPLE_DISPLAYS
-define:ENABLE_PHYSICS
-define:ENABLE_TEXTURE_STREAMING
-define:ENABLE_VIRTUALTEXTURING
-define:ENABLE_LZMA
-define:ENABLE_UNITYEVENTS
-define:ENABLE_VR
-define:ENABLE_WEBCAM
-define:ENABLE_UNITYWEBREQUEST
-define:ENABLE_WWW
-define:ENABLE_CLOUD_SERVICES
-define:ENABLE_CLOUD_SERVICES_ADS
-define:ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
-define:ENABLE_CLOUD_SERVICES_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_PURCHASING
-define:ENABLE_CLOUD_SERVICES_ANALYTICS
-define:ENABLE_CLOUD_SERVICES_BUILD
-define:ENABLE_EDITOR_GAME_SERVICES
-define:ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT
-define:ENABLE_CLOUD_LICENSE
-define:ENABLE_EDITOR_HUB_LICENSE
-define:ENABLE_WEBSOCKET_CLIENT
-define:ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API
-define:ENABLE_DIRECTOR_AUDIO
-define:ENABLE_DIRECTOR_TEXTURE
-define:ENABLE_MANAGED_JOBS
-define:ENABLE_MANAGED_TRANSFORM_JOBS
-define:ENABLE_MANAGED_ANIMATION_JOBS
-define:ENABLE_MANAGED_AUDIO_JOBS
-define:ENABLE_MANAGED_UNITYTLS
-define:INCLUDE_DYNAMIC_GI
-define:ENABLE_SCRIPTING_GC_WBARRIERS
-define:PLATFORM_SUPPORTS_MONO
-define:RENDER_SOFTWARE_CURSOR
-define:ENABLE_VIDEO
-define:ENABLE_ACCELERATOR_CLIENT_DEBUGGING
-define:ENABLE_NAVIGATION_PACKAGE_DEBUG_VISUALIZATION
-define:ENABLE_NAVIGATION_HEIGHTMESH_RUNTIME_SUPPORT
-define:ENABLE_NAVIGATION_UI_REQUIRES_PACKAGE
-define:PLATFORM_STANDALONE
-define:TEXTCORE_1_0_OR_NEWER
-define:PLATFORM_STANDALONE_WIN
-define:UNITY_STANDALONE_WIN
-define:UNITY_STANDALONE
-define:UNITY_UGP_API
-define:ENABLE_RUNTIME_GI
-define:ENABLE_MOVIES
-define:ENABLE_NETWORK
-define:ENABLE_NVIDIA
-define:ENABLE_CRUNCH_TEXTURE_COMPRESSION
-define:ENABLE_OUT_OF_PROCESS_CRASH_HANDLER
-define:ENABLE_CLUSTER_SYNC
-define:ENABLE_CLUSTERINPUT
-define:PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP
-define:GFXDEVICE_WAITFOREVENT_MESSAGEPUMP
-define:PLATFORM_INITIALIZES_MEMORY_MANAGER_EXPLICITLY
-define:ENABLE_MONO
-define:NET_4_6
-define:NET_UNITY_4_8
-define:ENABLE_PROFILER
-define:DEBUG
-define:TRACE
-define:UNITY_ASSERTIONS
-define:UNITY_EDITOR
-define:UNITY_EDITOR_64
-define:UNITY_EDITOR_WIN
-define:ENABLE_UNITY_COLLECTIONS_CHECKS
-define:ENABLE_BURST_AOT
-define:UNITY_TEAM_LICENSE
-define:ENABLE_CUSTOM_RENDER_TEXTURE
-define:ENABLE_DIRECTOR
-define:ENABLE_LOCALIZATION
-define:ENABLE_SPRITES
-define:ENABLE_TERRAIN
-define:ENABLE_TILEMAP
-define:ENABLE_TIMELINE
-define:ENABLE_LEGACY_INPUT_MANAGER
-define:TEXTCORE_FONT_ENGINE_1_5_OR_NEWER
-define:UEE
-define:DOTWEEN
-define:CSHARP_7_OR_LATER
-define:CSHARP_7_3_OR_NEWER
-define:UNITY_EDITOR_ONLY_COMPILATION
-r:"Assets/Plugins/Borodar/RainbowFolders/Editor/Assemblies/RainbowFolders.dll"
-r:"Assets/Plugins/Demigiant/DemiLib/Core/DemiLib.dll"
-r:"Assets/Plugins/Demigiant/DemiLib/Core/Editor/DemiEditor.dll"
-r:"Assets/Plugins/Demigiant/DOTween/DOTween.dll"
-r:"Assets/Plugins/Demigiant/DOTween/Editor/DOTweenEditor.dll"
-r:"Assets/Plugins/Demigiant/DOTweenPro/DOTweenPro.dll"
-r:"Assets/Plugins/Demigiant/DOTweenPro/Editor/DOTweenProEditor.dll"
-r:"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Libraries/Harmony.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEditor.Graphs.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.DeviceSimulatorModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.DiagnosticsModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.EditorToolbarModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphViewModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.PresetsUIModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.QuickSearchModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneViewModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreFontEngineModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreTextEngineModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.UIBuilderModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AIModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AnimationModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ARModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AudioModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ClothModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterInputModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ContentLoadModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.CoreModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.DirectorModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.GameCenterModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.GIModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.GridModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.HotReloadModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.IMGUIModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.InputModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.LocalizationModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.NVIDIAModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.Physics2DModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.PhysicsModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ProfilerModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.PropertiesModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.StreamingModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.SubstanceModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreFontEngineModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreTextEngineModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TilemapModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TLSModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UIModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UmbraModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.VehiclesModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.VFXModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.VideoModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.VirtualTexturingModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.VRModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.WindModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.XRModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/Microsoft.Win32.Primitives.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/netstandard.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.AppContext.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Buffers.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.Concurrent.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.NonGeneric.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.Specialized.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.Annotations.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.EventBasedAsync.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.Primitives.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.TypeConverter.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Console.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Data.Common.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Contracts.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Debug.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.FileVersionInfo.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Process.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.StackTrace.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.TextWriterTraceListener.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Tools.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.TraceSource.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Drawing.Primitives.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Dynamic.Runtime.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.Calendars.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.Extensions.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.Compression.ZipFile.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.DriveInfo.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.Primitives.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.Watcher.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.IsolatedStorage.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.MemoryMappedFiles.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.Pipes.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.UnmanagedMemoryStream.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Expressions.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Parallel.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Queryable.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Memory.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Http.Rtc.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.NameResolution.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.NetworkInformation.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Ping.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Primitives.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Requests.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Security.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Sockets.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebHeaderCollection.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebSockets.Client.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebSockets.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ObjectModel.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.DispatchProxy.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.ILGeneration.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.Lightweight.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Extensions.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Primitives.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.Reader.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.ResourceManager.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.Writer.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.CompilerServices.VisualC.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Extensions.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Handles.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.RuntimeInformation.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.WindowsRuntime.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Numerics.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Formatters.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Json.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Primitives.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Xml.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Claims.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Algorithms.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Csp.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Encoding.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Primitives.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.X509Certificates.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Principal.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.SecureString.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Duplex.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Http.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.NetTcp.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Primitives.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Security.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.Encoding.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.Encoding.Extensions.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.RegularExpressions.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Overlapped.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.Extensions.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.Parallel.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Thread.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.ThreadPool.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Timer.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ValueTuple.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.ReaderWriter.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XDocument.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XmlDocument.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XmlSerializer.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XPath.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XPath.XDocument.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Microsoft.CSharp.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/mscorlib.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.ComponentModel.Composition.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Core.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Data.DataSetExtensions.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Data.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Drawing.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.IO.Compression.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.IO.Compression.FileSystem.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Net.Http.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Numerics.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Numerics.Vectors.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Runtime.Serialization.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Transactions.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Xml.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Xml.Linq.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@2.9.2/Lib/Editor/log4netPlastic.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@2.9.2/Lib/Editor/Unity.Plastic.Antlr3.Runtime.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@2.9.2/Lib/Editor/Unity.Plastic.Newtonsoft.Json.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@2.9.2/Lib/Editor/unityplastic.dll"
-r:"Library/PackageCache/com.unity.ext.nunit@1.0.6/net35/unity-custom/nunit.framework.dll"
-r:"Library/PackageCache/com.unity.nuget.newtonsoft-json@3.2.1/Runtime/Newtonsoft.Json.dll"
-r:"Library/PackageCache/com.unity.visualscripting@1.9.8/Editor/VisualScripting.Core/Dependencies/DotNetZip/Unity.VisualScripting.IonicZip.dll"
-r:"Library/PackageCache/com.unity.visualscripting@1.9.8/Editor/VisualScripting.Core/Dependencies/YamlDotNet/Unity.VisualScripting.YamlDotNet.dll"
-r:"Library/PackageCache/com.unity.visualscripting@1.9.8/Editor/VisualScripting.Core/EditorAssetResources/Unity.VisualScripting.TextureAssets.dll"
-r:"Library/PackageCache/com.unity.visualscripting@1.9.8/Runtime/VisualScripting.Flow/Dependencies/NCalc/Unity.VisualScripting.Antlr3.Runtime.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/UltimateEditorEnhancer.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.ref.dll"
-analyzer:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Tools/Unity.SourceGenerators/Unity.Properties.SourceGenerator.dll"
-analyzer:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Tools/Unity.SourceGenerators/Unity.SourceGenerators.dll"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Attributes/ComponentHeaderButtonAttribute.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Attributes/HideInIntegrityAttribute.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Attributes/RequireComponentsAttribute.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Attributes/RequireMultipleGameObjectsAttribute.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Attributes/RequireSelectedAttribute.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Attributes/RuntimeOnlyAttribute.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Attributes/TitleAttribute.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Attributes/ValidateAttribute.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Behaviours/AddComponentBehavior.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Behaviours/AutoSave.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Behaviours/CollectionSelector.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Behaviours/CreateDirectory.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Behaviours/DragAndDropBehavior.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Behaviours/DragAndDropToEventField.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Behaviours/DragAndDropToTab.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Behaviours/Group.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Behaviours/LODGroupSelector.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Behaviours/MaximizeGameView.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Behaviours/Rename.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Behaviours/Replace.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Behaviours/SelectCustomTool.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Behaviours/SelectionHistory.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Behaviours/SelectPrefabInstances.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Behaviours/Switcher.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Behaviours/Ungroup.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/ComponentHeaderItems/AlignAndDistribute.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/ComponentHeaderItems/BoxColliderDetectSize.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/ComponentHeaderItems/CameraAlignWith.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/ComponentHeaderItems/ComponentBookmark.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/ComponentHeaderItems/CopyPaste.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/ComponentHeaderItems/MoveComponentAboveBelow.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/ComponentHeaderItems/RuntimeSaveButton.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/ComponentHeaderItems/TransformInspectorGlobalValues.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Context Menu/Actions/ActionItem.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Context Menu/Actions/AddComponent.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Context Menu/Actions/Align.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Context Menu/Actions/CloseComponentWindows.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Context Menu/Actions/Create.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Context Menu/Actions/Group.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Context Menu/Actions/History.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Context Menu/Actions/MaximizeSceneView.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Context Menu/Actions/Replace.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Context Menu/Actions/SceneViewActions.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Context Menu/Actions/SelectGameObject.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Context Menu/Actions/ShowSettings.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Context Menu/Actions/Snapping.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Context Menu/Actions/UpdateAvailable.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Context Menu/EditorMenu.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Context Menu/Layouts/ActionsLayout.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Context Menu/Layouts/BreadcrumbsLayout.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Context Menu/Layouts/LayoutItem.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Context Menu/Layouts/MainLayoutItem.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Context Menu/Layouts/MainLayoutItemGeneric.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Context Menu/Layouts/WindowsLayout.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Context Menu/PopupWindows/Bookmarks.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Context Menu/PopupWindows/Components.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Context Menu/PopupWindows/FavoriteWindows.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Context Menu/PopupWindows/Hierarchy.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Context Menu/PopupWindows/Inspector.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Context Menu/PopupWindows/PopupWindowItem.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Context Menu/PopupWindows/Project.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Core/CompilerDefine.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Core/ComponentHeaderContextMenuInjection.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Core/Log.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Core/Settings.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Core/Version.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/CustomPropertyDrawers/EmptyInspectorItemDrawer.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/CustomPropertyDrawers/ObjectFieldDrawer.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/CustomPropertyDrawers/UnityEventBaseDrawer.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Editors/DocumentationEditor.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Editors/LightEditorExt.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Editors/MissedScriptEditor.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Editors/ViewStateReadmeEditor.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Enums/AutoSize.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Enums/BackgroundCondition.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Enums/ButtonEvent.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Enums/CreateBrowserTarget.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Enums/HeaderCondition.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Enums/HierarchyIconsDisplayRule.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Enums/NestedEditorSide.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Enums/QuickAccessItemIcon.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Enums/QuickAccessItemType.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Enums/QuickAccessWindowMode.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Enums/SceneViewVisibleRules.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Enums/SmartSelectionStyle.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Enums/TimerMode.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Enums/ToolbarAlign.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Helpers/CanvasUtils.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Helpers/ComponentHeaderButtonOrder.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Helpers/ComponentUtils.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Helpers/DelegateHelper.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Helpers/EditorIconContents.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Helpers/EditorResources.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Helpers/FixedIDs.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Helpers/GameObjectUtils.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Helpers/GenericMenuEx.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Helpers/GenericMenuItem.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Helpers/GlobalObjectIdHelper.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Helpers/GUILayoutUtils.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Helpers/HierarchyHelper.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Helpers/HierarchyToolOrder.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Helpers/Icons.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Helpers/LayoutHelper.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Helpers/Links.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Helpers/MathHelper.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Helpers/MenuItemOrder.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Helpers/OddEvenRowDrawer.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Helpers/PackageLocator.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Helpers/PositionHandle.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Helpers/ProjectAssetCache.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Helpers/ProjectToolOrder.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Helpers/RecordUpgrader.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Helpers/RenderPipelineHelper.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Helpers/Resources.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Helpers/ResourcesCache.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Helpers/SceneManagerHelper.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Helpers/SceneReferencesLoader.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Helpers/SceneViewHelper.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Helpers/SceneViewOrder.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Helpers/SerializedPropertyHelper.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Helpers/SnapHelper.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Helpers/StringHelper.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Helpers/StringWithNumberComparer.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Helpers/Styles.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Helpers/StyleSheets.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Helpers/TempContent.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Helpers/TemporaryObjectManager.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Helpers/VisualElementHelper.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Helpers/WindowsHelper.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Hierarchy/BackgroundDrawer.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Hierarchy/BackgroundRule.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Hierarchy/BestIconDrawer.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Hierarchy/BookmarkButton.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Hierarchy/ComponentIconDrawer.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Hierarchy/ErrorDrawer.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Hierarchy/Header.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Hierarchy/HeaderRule.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Hierarchy/Highlighter.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Hierarchy/IconSelector.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Hierarchy/NoteDrawer.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Hierarchy/OddEven.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Hierarchy/RowEnable.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Hierarchy/SoloPickability.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Hierarchy/SoloVisibility.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Hierarchy/TreeDrawer.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Improvements/CurveEditorWindowImprovement.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Inspector/AnimatorInspectorClips.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Inspector/ComponentEditor.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Inspector/ComponentExporter.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Inspector/EmptyInspector.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Inspector/InspectorBar.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Inspector/InspectorInjector.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Inspector/LongTextEditor.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Inspector/NestedEditor.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Inspector/ObjectFieldDragAndDrop.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Inspector/ObjectFieldSelector.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Inspector/TransformAlign.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Integration/Cinemachine.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Integration/EnhancedHierarchy.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Integration/FullscreenEditor.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Integration/ProGrids.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Interceptors/AnimatorInspectorInterceptor.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Interceptors/Base/GenericInterceptor.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Interceptors/Base/Interceptor.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Interceptors/Base/StatedInterceptor.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Interceptors/CurveEditorWindowGetCurveEditorInterceptor.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Interceptors/CurveEditorWindowOnGUIInterceptor.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Interceptors/DrawEditorHeaderItemsInterceptor.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Interceptors/DrawPresetButtonInterceptor.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Interceptors/EditorGUIDoObjectFieldInterceptor.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Interceptors/EditorGUIDoTextFieldInterceptor.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Interceptors/EnumPopupInterceptor.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Interceptors/GenericInterceptor.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Interceptors/HelpIconButtonInterceptor.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Interceptors/HierarchyToolbarInterceptor.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Interceptors/NumberFieldInterceptor.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Interceptors/ObjectFieldInterceptor.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Interceptors/PropertyHandlerInterceptor.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Interceptors/ReorderableListInterceptor.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Interceptors/TransformInspectorInterceptor.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Interceptors/TreeViewControllerUserInputChangedExpandedState.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Interfaces/IHasShortcutPref.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Interfaces/IInvokableLayoutItem.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Interfaces/IStateablePref.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Interfaces/IValidatableLayoutItem.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Managers/BindingManager.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Managers/EventManager.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Managers/GlobalEventManager.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Managers/HierarchyItem.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Managers/HierarchyItemDrawer.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Managers/InputManager.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Managers/KeyManager.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Managers/LocalSettings.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Managers/LogManager.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Managers/NoteManager.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Managers/ReferenceManager.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Managers/SceneViewManager.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Managers/SelectionBoundsManager.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Managers/SelectionManager.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Managers/ToolbarManager.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Managers/WindowManager.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/PostHeaderItems/HeaderBookmark.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/PostHeaderItems/HeaderFieldFilter.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/PostHeaderItems/HeaderNote.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/PostHeaderItems/NoteItem.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/PostHeaderItems/PostHeaderItem.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/PostHeaderItems/PostHeaderManager.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Prefs/Actions.Prefs.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Prefs/Autosave.Prefs.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Prefs/Backgrounds.Prefs.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Prefs/BackupScene.Prefs.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Prefs/Bookmarks.Prefs.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Prefs/Breadcrumbs.Prefs.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Prefs/ContextMenu.Prefs.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Prefs/CreateBrowser.Prefs.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Prefs/DistanceTool.Prefs.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Prefs/DropToFloor.Prefs.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Prefs/DuplicateTool.Prefs.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Prefs/EmptyInspector.Prefs.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Prefs/FavoriteWindows.Prefs.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Prefs/FrameSelectedBounds.Prefs.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Prefs/GameObject.Prefs.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Prefs/GeneralManagers.Prefs.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Prefs/Group.Prefs.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Prefs/Headers.Prefs.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Prefs/Hierarchy.Prefs.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Prefs/Highlight.Prefs.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Prefs/ImproveBehaviours.Prefs.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Prefs/Inspector.Prefs.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Prefs/JumpToPoint.Prefs.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Prefs/Log.Prefs.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Prefs/Main.Prefs.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Prefs/Methods.Prefs.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Prefs/MiniLayouts.Prefs.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Prefs/Navigation.Prefs.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Prefs/ObjectPlacer.Prefs.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Prefs/ObjectToolbar.Prefs.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Prefs/Order.Prefs.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Prefs/PopupWindows.Prefs.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Prefs/PrefManager.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Prefs/Preview.Prefs.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Prefs/Project.Prefs.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Prefs/ProjectFolderIconManager.Prefs.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Prefs/QuickAccessBar.Prefs.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Prefs/RemoveIconPrefix.Prefs.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Prefs/Rename.Prefs.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Prefs/Replace.Prefs.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Prefs/RotateByShortcut.Prefs.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Prefs/SceneReferences.Prefs.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Prefs/SceneView.Prefs.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Prefs/ScriptingDefineSymbols.Prefs.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Prefs/Search.Prefs.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Prefs/SelectionBounds.Prefs.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Prefs/SelectionHistory.Prefs.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Prefs/SettingsToolbar.Prefs.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Prefs/Shortcut.Prefs.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Prefs/StandalonePrefManager.Prefs.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Prefs/SwitchCustomTool.Prefs.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Prefs/Switcher.Prefs.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Prefs/TerrainBrushSize.Prefs.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Prefs/Toolbar.Prefs.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Prefs/ToolbarWindows.Prefs.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Prefs/ToolValues.Prefs.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Prefs/Ungroup.Prefs.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Prefs/Unsafe.Prefs.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Prefs/Updater.Prefs.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Prefs/ViewGallery.Prefs.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Prefs/Waila.Prefs.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Prefs/Window.Prefs.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Prefs/ZoomShortcut.Prefs.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Project/FileExtensions.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Project/OddEven.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Project/ProjectCreateCustomEditor.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Project/ProjectCreateFolder.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Project/ProjectCreateMaterial.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Project/ProjectCreateMaterialFromTexture.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Project/ProjectCreateScript.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Project/ProjectCreateShader.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Project/ProjectFolderIconDrawer.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Project/ProjectFolderRule.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Project/ProjectItem.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Project/ProjectItemDrawer.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Project/ProjectPlayAudio.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Scene View/DropToFloor.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Scene View/FloatToolbars/FloatToolbar.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Scene View/FloatToolbars/FloatToolbarManager.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Scene View/FloatToolbars/ObjectToolbar.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Scene View/FrameSelectedBounds.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Scene View/HighJumpToPoint.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Scene View/Highlighter.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Scene View/JumpToPoint.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Scene View/MoveToPoint.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Scene View/ObjectPlacer.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Scene View/Preview.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Scene View/QuickAccessBar/Actions/OpenAction.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Scene View/QuickAccessBar/Actions/QuickAccessAction.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Scene View/QuickAccessBar/Actions/SaveAction.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Scene View/QuickAccessBar/QuickAccess.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Scene View/QuickAccessBar/QuickAccessItem.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Scene View/RotateByShortcut.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Scene View/SceneViewAlignDrawer.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Scene View/SelectionSize.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Scene View/SmartSelection.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Scene View/TerrainBrushSize.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Scene View/ToolValues.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Scene View/ViewStateDrawer.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Scene View/Waila.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Scene View/ZoomShortcutBehaviour.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Toolbar/Timer.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Toolbar/Windows/ToolbarWindows.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Toolbar/Windows/ToolbarWindows.FavoriteProvider.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Toolbar/Windows/ToolbarWindows.MiniLayouts.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Toolbar/Windows/ToolbarWindows.OpenedProvider.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Toolbar/Windows/ToolbarWindows.Provider.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Toolbar/Windows/ToolbarWindows.RecentProvider.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Toolbar/Windows/ToolbarWindows.WindowRecord.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Tools/CustomPivotRotation.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Tools/DuplicateTool.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Tools/PivotTool.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/TransformEditorTools/AlignTool.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/TransformEditorTools/BoundsTool.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/TransformEditorTools/TransformEditorTool.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/UnityTypes/ActiveEditorTrackerRef.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/UnityTypes/AddComponentWindowRef.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/UnityTypes/AnimationUtilityRef.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/UnityTypes/AnimatorInspectorRef.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/UnityTypes/AssetImporterEditorRef.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/UnityTypes/AssetPreviewRef.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/UnityTypes/AssetsTreeViewDataSourceRef.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/UnityTypes/AudioUtilsRef.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/UnityTypes/Compatibility.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/UnityTypes/ComponentUtilityRef.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/UnityTypes/ConsoleWindowRef.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/UnityTypes/ContainerWindowRef.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/UnityTypes/CurveEditorWindowRef.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/UnityTypes/DockAreaRef.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/UnityTypes/EditorApplicationRef.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/UnityTypes/EditorElementRef.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/UnityTypes/EditorGUILayoutEx.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/UnityTypes/EditorGUINumberFieldValueRef.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/UnityTypes/EditorGUIRef.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/UnityTypes/EditorGUIUtilityRef.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/UnityTypes/EditorRef.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/UnityTypes/EditorToolUtilityRef.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/UnityTypes/EditorUtilityRef.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/UnityTypes/EditorWindowRef.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/UnityTypes/EventRef.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/UnityTypes/GameViewRef.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/UnityTypes/GridSettingsRef.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/UnityTypes/GUIContentRef.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/UnityTypes/GUILayoutGroupRef.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/UnityTypes/GUILayoutUtilityRef.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/UnityTypes/GUISkinRef.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/UnityTypes/GUIViewRef.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/UnityTypes/HandleUtilityRef.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/UnityTypes/HostViewRef.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/UnityTypes/IconSelectorRef.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/UnityTypes/IMGUIContainerRef.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/UnityTypes/InspectorWindowRef.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/UnityTypes/ITreeViewDataSourceRef.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/UnityTypes/IWindowBackendRef.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/UnityTypes/LogEntriesRef.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/UnityTypes/LogEntryRef.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/UnityTypes/ObjectListAreaRef.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/UnityTypes/ObjectListAreaStateRef.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/UnityTypes/ObjectSelectorRef.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/UnityTypes/PaintTreesToolRef.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/UnityTypes/PlayModeViewRef.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/UnityTypes/PopupCallbackInfoRef.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/UnityTypes/PrefabImporterRef.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/UnityTypes/PrefabStageUtilityRef.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/UnityTypes/PrefabUtilityRef.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/UnityTypes/PresetSelectorRef.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/UnityTypes/ProjectBrowserRef.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/UnityTypes/ProjectSettingsWindowRef.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/UnityTypes/ProjectWindowUtilsRef.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/UnityTypes/PropertyEditorRef.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/UnityTypes/PropertyHandlerRef.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/UnityTypes/RecycledTextEditorRef.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/UnityTypes/ReorderableListRef.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/UnityTypes/SceneHierarchyRef.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/UnityTypes/SceneHierarchyWindowRef.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/UnityTypes/SceneVisibilityManagerRef.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/UnityTypes/SceneVisibilityStateRef.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/UnityTypes/SearchableEditorWindowRef.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/UnityTypes/SearchFilterRef.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/UnityTypes/SelectionRef.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/UnityTypes/ShortcutIntegrationRef.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/UnityTypes/ShowModeRef.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/UnityTypes/SpriteUtilityRef.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/UnityTypes/TerrainInspectorRef.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/UnityTypes/TextEditorRef.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/UnityTypes/TextureImporterInspectorRef.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/UnityTypes/ToolbarRef.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/UnityTypes/TransformInspectorRef.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/UnityTypes/TreeViewControllerRef.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/UnityTypes/TreeViewGUIRef.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/UnityTypes/UnityEventDrawerRef.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/UnityTypes/ViewRef.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Windows/About.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Windows/AdvancedDropToFloor.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Windows/AutoSizePopupWindow.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Windows/Bookmarks/Bookmarks.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Windows/Bookmarks/Bookmarks.GridDrawer.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Windows/Bookmarks/Bookmarks.RowDrawer.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Windows/Bookmarks/ProjectFolderBookmark.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Windows/CheckIntegrityWindow.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Windows/ComponentWindow.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Windows/CreateBrowser/CreateBrowser.BookmarkProvider.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Windows/CreateBrowser/CreateBrowser.CreateItem.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Windows/CreateBrowser/CreateBrowser.CreateItemFolder.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Windows/CreateBrowser/CreateBrowser.CreateProvider.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Windows/CreateBrowser/CreateBrowser.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Windows/CreateBrowser/CreateBrowser.FolderItem.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Windows/CreateBrowser/CreateBrowser.Item.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Windows/CreateBrowser/CreateBrowser.PrefabItem.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Windows/CreateBrowser/CreateBrowser.PrefabItemFolder.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Windows/CreateBrowser/CreateBrowser.PrefabProvider.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Windows/CreateBrowser/CreateBrowser.Provider.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Windows/DistanceTool.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Windows/EditorIconsBrowser.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Windows/FavoriteWindow/FavoriteWindowItem.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Windows/FlatSelectorWindow.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Windows/FlatSmartSelectionWindow.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Windows/GameObjectHierarchySettings.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Windows/GettingStarted.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Windows/InputDialog.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Windows/LayoutWindow.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Windows/LongTextEditorWindow.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Windows/MigrationWindow.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Windows/NoteManager.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Windows/ObjectWindow.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Windows/PinAndClose.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Windows/PopupWindow.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Windows/SceneBackups.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Windows/SceneHistory/SceneHistory.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Windows/SceneHistory/SceneHistoryItem.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Windows/Search/Search.Cache.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Windows/Search/Search.ComponentRecord.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Windows/Search/Search.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Windows/Search/Search.GameObjectRecord.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Windows/Search/Search.ProjectRecord.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Windows/Search/Search.Record.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Windows/Search/Search.UI.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Windows/Search/Search.WindowRecord.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Windows/Shortcuts.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Windows/TimescaleWindow.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Windows/TransformEditorWindow.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Windows/Updater.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Windows/ViewGallery/SelectionViewStates.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Windows/ViewGallery/ViewGallery.CameraStateItem.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Windows/ViewGallery/ViewGallery.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Windows/ViewGallery/ViewGallery.ViewItem.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Windows/ViewGallery/ViewGallery.ViewStateItem.cs"
"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Scripts/Editor/Windows/Welcome.cs"
-langversion:9.0
/deterministic
/optimize+
/debug:portable
/nologo
/RuntimeMetadataVersion:v4.0.30319
/nowarn:0169
/nowarn:0649
/nowarn:0282
/nowarn:1701
/nowarn:1702
/utf8output
/preferreduilang:en-US
/additionalfile:"Library/Bee/artifacts/1900b0aE.dag/UltimateEditorEnhancer-Editor.UnityAdditionalFile.txt"