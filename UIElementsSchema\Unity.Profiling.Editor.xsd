<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:editor="UnityEditor.UIElements" xmlns:engine="UnityEngine.UIElements" xmlns="UnityEditor.Overlays" elementFormDefault="qualified" targetNamespace="Unity.Profiling.Editor" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:import schemaLocation="UnityEngine.UIElements.xsd" namespace="UnityEngine.UIElements" />
  <xs:simpleType name="SelectableLabel_keyboard-type_Type">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Default" />
      <xs:enumeration value="ASCIICapable" />
      <xs:enumeration value="NumbersAndPunctuation" />
      <xs:enumeration value="URL" />
      <xs:enumeration value="NumberPad" />
      <xs:enumeration value="PhonePad" />
      <xs:enumeration value="NamePhonePad" />
      <xs:enumeration value="EmailAddress" />
      <xs:enumeration value="NintendoNetworkAccount" />
      <xs:enumeration value="Social" />
      <xs:enumeration value="Search" />
      <xs:enumeration value="DecimalPad" />
      <xs:enumeration value="OneTimeCode" />
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="SelectableLabelType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:sequence minOccurs="0" maxOccurs="unbounded">
          <xs:element ref="engine:VisualElement" />
        </xs:sequence>
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="true" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:attribute default="" name="binding-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="label" type="xs:string" use="optional" />
        <xs:attribute default="" name="value" type="xs:string" use="optional" />
        <xs:attribute default="-1" name="max-length" type="xs:int" use="optional" />
        <xs:attribute default="false" name="password" type="xs:boolean" use="optional" />
        <xs:attribute default="*" name="mask-character" type="xs:string" use="optional" />
        <xs:attribute default="false" name="readonly" type="xs:boolean" use="optional" />
        <xs:attribute default="false" name="is-delayed" type="xs:boolean" use="optional" />
        <xs:attribute default="false" name="hide-mobile-input" type="xs:boolean" use="optional" />
        <xs:attribute default="Default" name="keyboard-type" xmlns:q1="Unity.Profiling.Editor" type="q1:SelectableLabel_keyboard-type_Type" use="optional" />
        <xs:attribute default="false" name="auto-correction" type="xs:boolean" use="optional" />
        <xs:attribute default="false" name="multiline" type="xs:boolean" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="SelectableLabel" substitutionGroup="engine:VisualElement" xmlns:q2="Unity.Profiling.Editor" type="q2:SelectableLabelType" />
  <xs:complexType name="MemoryUsageBreakdownType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:sequence minOccurs="0" maxOccurs="unbounded">
          <xs:element xmlns:q3="Unity.Profiling.Editor" ref="q3:MemoryUsageBreakdownElement" />
        </xs:sequence>
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="false" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:attribute default="Memory Usage" name="header-text" type="xs:string" use="optional" />
        <xs:attribute default="1288490240" name="total-bytes" type="xs:int" use="optional" />
        <xs:attribute default="false" name="show-unknown" type="xs:boolean" use="optional" />
        <xs:attribute default="Unknown" name="unknown-name" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="MemoryUsageBreakdown" substitutionGroup="engine:VisualElement" xmlns:q4="Unity.Profiling.Editor" type="q4:MemoryUsageBreakdownType" />
  <xs:complexType name="BackgroundPatternType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="false" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:attribute default="1" name="scale" type="xs:float" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="BackgroundPattern" substitutionGroup="engine:VisualElement" xmlns:q5="Unity.Profiling.Editor" type="q5:BackgroundPatternType" />
  <xs:complexType name="MemoryUsageBreakdownElementType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="false" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:attribute default="Other" name="text" type="xs:string" use="optional" />
        <xs:attribute default="" name="background-color-class" type="xs:string" use="optional" />
        <xs:attribute default="false" name="show-used" type="xs:boolean" use="optional" />
        <xs:attribute default="50" name="used-bytes" type="xs:long" use="optional" />
        <xs:attribute default="100" name="total-bytes" type="xs:long" use="optional" />
        <xs:attribute default="false" name="show-selected" type="xs:boolean" use="optional" />
        <xs:attribute default="0" name="selected-bytes" type="xs:long" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="MemoryUsageBreakdownElement" substitutionGroup="engine:VisualElement" xmlns:q6="Unity.Profiling.Editor" type="q6:MemoryUsageBreakdownElementType" />
</xs:schema>