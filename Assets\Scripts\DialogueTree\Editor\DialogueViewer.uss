GridBackground {
    --grid-background-color: rgb(40, 40, 40);
    --line-color: rgba(193, 196, 192, 0.02);
    --thick-line-color: rgba(193, 196, 192, 0.03);
    --spacing: 20;
}

#title-label {
    -unity-font-style: bold;
    -unity-text-align: middle-center;
    font-size: 16px;
    margin-top: 0;
    margin-left: 0;
    border-left-width: 0;
    border-right-width: 0;
    border-top-width: 0;
    border-bottom-width: 0;
}

#node-border {
    background-color: rgba(56, 56, 56, 0.82);
    border-left-color: rgb(32, 32, 32);
    border-right-color: rgb(32, 32, 32);
    border-top-color: rgb(32, 32, 32);
    border-bottom-color: rgb(32, 32, 32);
    border-top-width: 1px;
    border-right-width: 1px;
    border-bottom-width: 1px;
    border-left-width: 1px;
    border-top-left-radius: 3px;
    border-top-right-radius: 3px;
    border-bottom-right-radius: 3px;
    border-bottom-left-radius: 3px;
    -unity-slice-left: 0;
    -unity-slice-top: 0;
    -unity-slice-right: 0;
    -unity-slice-bottom: 0;
}

#selection-border {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    border-left-color: rgb(47, 114, 168);
    border-right-color: rgb(47, 114, 168);
    border-top-color: rgb(47, 114, 168);
    border-bottom-color: rgb(47, 114, 168);
    border-top-left-radius: 3px;
    border-top-right-radius: 3px;
    border-bottom-right-radius: 3px;
    border-bottom-left-radius: 3px;
    border-top-width: 0;
    border-right-width: 0;
    border-bottom-width: 0;
    border-left-width: 0;
}

#input {
    align-items: center;
    align-self: center;
    justify-content: center;
    min-width: auto;
    min-height: 6px;
    background-color: rgba(99, 62, 62, 0.82);
    border-top-left-radius: 3px;
    border-top-right-radius: 3px;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
    border-top-width: 0;
    border-right-width: 0;
    border-bottom-width: 0;
    border-left-width: 0;
    -unity-slice-left: 0;
    -unity-slice-top: 0;
    -unity-slice-right: 0;
    -unity-slice-bottom: 0;
    -unity-slice-scale: 1px;
    border-left-color: rgba(0, 0, 0, 0);
    border-right-color: rgba(0, 0, 0, 0);
    border-top-color: rgba(0, 0, 0, 0);
    border-bottom-color: rgba(0, 0, 0, 0);
    flex-shrink: 1;
    max-height: 6px;
    flex-grow: 1;
    margin-top: 3px;
}

#output {
    flex-direction: row;
    align-items: stretch;
    align-self: center;
    justify-content: flex-start;
    background-color: rgba(164, 143, 63, 0.71);
    border-bottom-right-radius: 3px;
    border-bottom-left-radius: 3px;
    min-width: 72px;
    min-height: 6px;
    max-height: 6px;
    border-right-width: 3px;
    border-left-width: 3px;
    flex-grow: 0;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
    -unity-slice-left: 0;
    -unity-slice-right: 0;
    padding-right: 0;
    padding-left: 0;
    margin-right: 0;
    margin-left: 0;
    margin-bottom: 3px;
    margin-top: 0;
    padding-top: 0;
    border-bottom-width: 0;
    -unity-slice-bottom: 0;
}

#divider {
    min-height: auto;
    background-color: rgb(32, 32, 32);
    color: rgb(40, 40, 40);
}

#BranchingDialogue {
    flex-direction: row;
    justify-content: center;
    flex-grow: 0;
    align-self: stretch;
    align-items: stretch;
}

#textField {
    min-width: 56px;
    max-width: 202px;
    flex-grow: 1;
}

#RandomField {
    max-width: none;
    min-width: 28px;
}

#RandomIcon {
    margin-left: 3px;
}

#RootIcon {
    max-height: 36px;
    align-items: stretch;
    align-self: stretch;
    justify-content: flex-start;
    transform-origin: center;
    scale: 1.8 1;
    translate: 0 0;
}

#EndIcon {
    scale: 2 1;
    max-height: 21px;
    translate: 2px 0.7px;
    -unity-text-align: middle-center;
    align-self: auto;
    justify-content: flex-start;
    align-items: stretch;
}

:hover > #selection-border {
    border-top-width: 1px;
    border-right-width: 1px;
    border-bottom-width: 1px;
    border-left-width: 1px;
}

:selected > #selection-border {
    border-top-width: 1px;
    border-right-width: 1px;
    border-bottom-width: 1px;
    border-left-width: 1px;
}

:hover:selected > #selection-border {
    border-top-width: 2px;
    border-right-width: 2px;
    border-bottom-width: 2px;
    border-left-width: 2px;
}

.node {
    min-width: 88px;
    margin-top: 0;
    margin-right: 0;
    margin-bottom: 0;
    margin-left: 0;
}

.port {
    max-height: 12px;
    min-width: auto;
    flex-grow: 1;
    width: auto;
    max-width: none;
    display: flex;
    margin-left: 6px;
    margin-right: 6px;
    align-self: center;
    justify-content: space-between;
    align-items: center;
    position: relative;
    left: auto;
    translate: 0 0;
}

.connectorText {
    min-width: 0;
    min-height: 0;
    max-width: 0;
    max-height: 0;
    height: 0;
    width: 0;
    justify-content: center;
    align-items: center;
    align-self: center;
    -unity-text-align: middle-center;
    font-size: 1px;
    -unity-font-style: normal;
    color: rgb(253, 25, 25);
    margin-top: 0;
    display: flex;
}

.connectorBox {
    min-width: 10px;
    min-height: 10px;
    max-width: none;
    max-height: none;
    align-items: center;
    justify-content: center;
    align-self: center;
    margin-top: 0;
    margin-right: 0;
    margin-bottom: 0;
    margin-left: 0;
    padding-top: 0;
    padding-right: 0;
    padding-bottom: 0;
    padding-left: 0;
    -unity-slice-left: 0;
    -unity-slice-top: 0;
    -unity-slice-right: 0;
    -unity-slice-bottom: 0;
    border-top-width: 0;
    border-right-width: 0;
    border-bottom-width: 0;
    border-left-width: 0;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
    width: auto;
    height: auto;
    flex-grow: 0;
    flex-direction: column;
    -unity-text-align: upper-left;
    -unity-font-style: normal;
    position: absolute;
    top: -4px;
    bottom: -1px;
    display: flex;
    right: auto;
    left: auto;
    translate: 0 0;
}

.connectorCap {
    min-width: 6px;
    min-height: 6px;
    margin-top: 0;
    margin-right: 0;
    margin-bottom: 0;
    margin-left: 0;
    position: absolute;
    left: 1px;
    top: 1px;
    right: 0;
    bottom: 0;
    flex-grow: 1;
    display: flex;
    border-left-width: 0;
}

.text-label {
    -unity-text-align: middle-left;
    font-size: 14px;
    -unity-font: url('project://database/Assets/TextMesh%20Pro/Fonts/Ailibaba.ttf?fileID=12800000&guid=3e3731135f2a51742afbcc12eb40fb8d&type=3#Ailibaba');
    max-width: 128px;
    margin-right: 3px;
    margin-left: 3px;
    margin-bottom: 3px;
}

.nodeImplement #title {
    background-color: rgba(188, 146, 50, 0.71);
}

.nodeSequence #title {
    background-color: rgba(58, 162, 181, 0.71);
}

.nodeBranching #title {
    background-color: rgba(47, 98, 204, 0.71);
}

.nodeBranchingCond #title {
    background-color: rgba(87, 32, 221, 0.71);
}

.nodeCondition #title {
    background-color: rgba(158, 58, 181, 0.71);
}

.nodeRandom #title {
    background-color: rgba(151, 181, 58, 0.71);
}

.nodeShout #title {
    background-color: rgba(55, 173, 172, 0.82);
}

.nodeRoot #title {
    background-color: rgba(57, 180, 64, 0.71);
}

.nodeEnd #title {
    background-color: rgba(180, 57, 61, 0.71);
}

.nodeBranching #BranchingPort {
    margin-left: 3px;
    margin-right: 0;
}

.nodeBranching #BranchingPort #connector {
    top: 1px;
    left: -1px;
}

.nodeCondition #ConditionFunction {
    flex-direction: row;
}

#ConditionPort {
    max-width: none;
    margin-right: 3px;
    margin-left: 0;
    align-self: center;
    align-items: stretch;
    justify-content: flex-start;
    flex-grow: 0;
}

#ConditionPort #connector {
    left: 0;
    top: 1px;
    justify-content: flex-start;
    align-self: auto;
    align-items: auto;
}

#ConditionButton {
    flex-grow: 1;
}

.nodeCondition #CondOut {
    min-width: auto;
    max-width: none;
    max-height: 12px;
    min-height: 12px;
    margin-left: 3px;
    margin-right: 3px;
}

.nodeCondition #CondOut #connector {
    left: 0;
    top: 0;
    right: -1px;
    bottom: 0;
}

.nodeBranchingCond #ConditionFunction {
    flex-direction: row;
}

#objectField {
    max-width: 128px;
    flex-grow: 1;
    align-self: stretch;
    justify-content: space-around;
    align-items: stretch;
}
