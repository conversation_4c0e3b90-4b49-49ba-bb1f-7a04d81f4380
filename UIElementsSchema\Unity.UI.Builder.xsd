<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:editor="UnityEditor.UIElements" xmlns:engine="UnityEngine.UIElements" xmlns="UnityEditor.Overlays" elementFormDefault="qualified" targetNamespace="Unity.UI.Builder" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:import schemaLocation="UnityEngine.UIElements.xsd" namespace="UnityEngine.UIElements" />
  <xs:complexType name="TextShadowStyleFieldType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:sequence minOccurs="0" maxOccurs="unbounded">
          <xs:element ref="engine:VisualElement" />
        </xs:sequence>
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="true" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:attribute default="" name="binding-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="label" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="TextShadowStyleField" substitutionGroup="engine:VisualElement" xmlns:q1="Unity.UI.Builder" type="q1:TextShadowStyleFieldType" />
  <xs:complexType name="FoldoutColorFieldType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="false" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:attribute default="" name="binding-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="text" type="xs:string" use="optional" />
        <xs:attribute default="" name="binding-paths" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="FoldoutColorField" substitutionGroup="engine:VisualElement" xmlns:q2="Unity.UI.Builder" type="q2:FoldoutColorFieldType" />
  <xs:complexType name="BuilderStyleRowType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:sequence minOccurs="0" maxOccurs="unbounded">
          <xs:element ref="engine:VisualElement" />
        </xs:sequence>
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="false" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:attribute default="" name="binding-path" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="BuilderStyleRow" substitutionGroup="engine:VisualElement" xmlns:q3="Unity.UI.Builder" type="q3:BuilderStyleRowType" />
  <xs:complexType name="FoldoutWithCheckboxType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="false" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:attribute default="" name="binding-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="text" type="xs:string" use="optional" />
        <xs:attribute default="false" name="value" type="xs:boolean" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="FoldoutWithCheckbox" substitutionGroup="engine:VisualElement" xmlns:q4="Unity.UI.Builder" type="q4:FoldoutWithCheckboxType" />
  <xs:complexType name="OverlayPainterHelperElementType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:sequence minOccurs="0" maxOccurs="unbounded">
          <xs:element ref="engine:VisualElement" />
        </xs:sequence>
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Ignore" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="false" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="OverlayPainterHelperElement" substitutionGroup="engine:VisualElement" xmlns:q5="Unity.UI.Builder" type="q5:OverlayPainterHelperElementType" />
  <xs:complexType name="LibraryFoldoutType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="false" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:attribute default="" name="binding-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="text" type="xs:string" use="optional" />
        <xs:attribute default="false" name="value" type="xs:boolean" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="LibraryFoldout" substitutionGroup="engine:VisualElement" xmlns:q6="Unity.UI.Builder" type="q6:LibraryFoldoutType" />
  <xs:complexType name="PersistedFoldoutType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="false" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:attribute default="" name="binding-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="text" type="xs:string" use="optional" />
        <xs:attribute default="false" name="value" type="xs:boolean" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="PersistedFoldout" substitutionGroup="engine:VisualElement" xmlns:q7="Unity.UI.Builder" type="q7:PersistedFoldoutType" />
  <xs:complexType name="AngleStyleFieldType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:sequence minOccurs="0" maxOccurs="unbounded">
          <xs:element ref="engine:VisualElement" />
        </xs:sequence>
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="true" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:attribute default="" name="binding-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="label" type="xs:string" use="optional" />
        <xs:attribute default="true" name="show-options" type="xs:boolean" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="AngleStyleField" substitutionGroup="engine:VisualElement" xmlns:q8="Unity.UI.Builder" type="q8:AngleStyleFieldType" />
  <xs:complexType name="UnityUIBuilderSelectionMarkerType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:sequence minOccurs="0" maxOccurs="unbounded">
          <xs:element ref="engine:VisualElement" />
        </xs:sequence>
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="false" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="UnityUIBuilderSelectionMarker" substitutionGroup="engine:VisualElement" xmlns:q9="Unity.UI.Builder" type="q9:UnityUIBuilderSelectionMarkerType" />
  <xs:complexType name="CategoryDropdownFieldType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:sequence minOccurs="0" maxOccurs="unbounded">
          <xs:element ref="engine:VisualElement" />
        </xs:sequence>
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="true" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:attribute default="" name="binding-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="label" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="CategoryDropdownField" substitutionGroup="engine:VisualElement" xmlns:q10="Unity.UI.Builder" type="q10:CategoryDropdownFieldType" />
  <xs:complexType name="BuilderNotificationsType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:sequence minOccurs="0" maxOccurs="unbounded">
          <xs:element ref="engine:VisualElement" />
        </xs:sequence>
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="false" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="BuilderNotifications" substitutionGroup="engine:VisualElement" xmlns:q11="Unity.UI.Builder" type="q11:BuilderNotificationsType" />
  <xs:complexType name="BuilderCanvasStyleControlsType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:sequence minOccurs="0" maxOccurs="unbounded">
          <xs:element ref="engine:VisualElement" />
        </xs:sequence>
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="false" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="BuilderCanvasStyleControls" substitutionGroup="engine:VisualElement" xmlns:q12="Unity.UI.Builder" type="q12:BuilderCanvasStyleControlsType" />
  <xs:complexType name="TranslateStyleFieldType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:sequence minOccurs="0" maxOccurs="unbounded">
          <xs:element ref="engine:VisualElement" />
        </xs:sequence>
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="true" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:attribute default="" name="binding-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="label" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="TranslateStyleField" substitutionGroup="engine:VisualElement" xmlns:q13="Unity.UI.Builder" type="q13:TranslateStyleFieldType" />
  <xs:complexType name="BuilderMoverType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:sequence minOccurs="0" maxOccurs="unbounded">
          <xs:element ref="engine:VisualElement" />
        </xs:sequence>
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="false" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="BuilderMover" substitutionGroup="engine:VisualElement" xmlns:q14="Unity.UI.Builder" type="q14:BuilderMoverType" />
  <xs:complexType name="BuilderTooltipPreviewType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:sequence minOccurs="0" maxOccurs="unbounded">
          <xs:element ref="engine:VisualElement" />
        </xs:sequence>
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="false" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:attribute default="" name="binding-path" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="BuilderTooltipPreview" substitutionGroup="engine:VisualElement" xmlns:q15="Unity.UI.Builder" type="q15:BuilderTooltipPreviewType" />
  <xs:complexType name="PercentSliderType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:sequence minOccurs="0" maxOccurs="unbounded">
          <xs:element ref="engine:VisualElement" />
        </xs:sequence>
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="true" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:attribute default="" name="binding-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="label" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="PercentSlider" substitutionGroup="engine:VisualElement" xmlns:q16="Unity.UI.Builder" type="q16:PercentSliderType" />
  <xs:complexType name="RotateStyleFieldType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:sequence minOccurs="0" maxOccurs="unbounded">
          <xs:element ref="engine:VisualElement" />
        </xs:sequence>
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="true" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:attribute default="" name="binding-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="label" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="RotateStyleField" substitutionGroup="engine:VisualElement" xmlns:q17="Unity.UI.Builder" type="q17:RotateStyleFieldType" />
  <xs:complexType name="NumericStyleFieldType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:sequence minOccurs="0" maxOccurs="unbounded">
          <xs:element ref="engine:VisualElement" />
        </xs:sequence>
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="true" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:attribute default="" name="binding-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="label" type="xs:string" use="optional" />
        <xs:attribute default="true" name="show-options" type="xs:boolean" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="NumericStyleField" substitutionGroup="engine:VisualElement" xmlns:q18="Unity.UI.Builder" type="q18:NumericStyleFieldType" />
  <xs:complexType name="ImageStyleFieldType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:sequence minOccurs="0" maxOccurs="unbounded">
          <xs:element ref="engine:VisualElement" />
        </xs:sequence>
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="true" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:attribute default="" name="binding-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="label" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="ImageStyleField" substitutionGroup="engine:VisualElement" xmlns:q19="Unity.UI.Builder" type="q19:ImageStyleFieldType" />
  <xs:complexType name="FoldoutNumberFieldType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="false" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:attribute default="" name="binding-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="text" type="xs:string" use="optional" />
        <xs:attribute default="" name="binding-paths" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="FoldoutNumberField" substitutionGroup="engine:VisualElement" xmlns:q20="Unity.UI.Builder" type="q20:FoldoutNumberFieldType" />
  <xs:complexType name="FontStyleStripType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:sequence minOccurs="0" maxOccurs="unbounded">
          <xs:element ref="engine:VisualElement" />
        </xs:sequence>
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="true" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:attribute default="" name="binding-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="label" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="FontStyleStrip" substitutionGroup="engine:VisualElement" xmlns:q21="Unity.UI.Builder" type="q21:FontStyleStripType" />
  <xs:complexType name="BuilderCanvasType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:sequence minOccurs="0" maxOccurs="unbounded">
          <xs:element ref="engine:VisualElement" />
        </xs:sequence>
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="false" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="BuilderCanvas" substitutionGroup="engine:VisualElement" xmlns:q22="Unity.UI.Builder" type="q22:BuilderCanvasType" />
  <xs:complexType name="HelpBoxType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:sequence minOccurs="0" maxOccurs="unbounded">
          <xs:element ref="engine:VisualElement" />
        </xs:sequence>
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="false" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:attribute default="" name="binding-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="text" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="HelpBox" substitutionGroup="engine:VisualElement" xmlns:q23="Unity.UI.Builder" type="q23:HelpBoxType" />
  <xs:complexType name="ModalPopupType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="false" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:attribute default="" name="title" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="ModalPopup" substitutionGroup="engine:VisualElement" xmlns:q24="Unity.UI.Builder" type="q24:ModalPopupType" />
  <xs:complexType name="TransformOriginSelectorType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:sequence minOccurs="0" maxOccurs="unbounded">
          <xs:element ref="engine:VisualElement" />
        </xs:sequence>
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="false" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="TransformOriginSelector" substitutionGroup="engine:VisualElement" xmlns:q25="Unity.UI.Builder" type="q25:TransformOriginSelectorType" />
  <xs:complexType name="BuilderNewSelectorFieldType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:sequence minOccurs="0" maxOccurs="unbounded">
          <xs:element ref="engine:VisualElement" />
        </xs:sequence>
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="false" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="BuilderNewSelectorField" substitutionGroup="engine:VisualElement" xmlns:q26="Unity.UI.Builder" type="q26:BuilderNewSelectorFieldType" />
  <xs:complexType name="BuilderSelectionIndicatorType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:sequence minOccurs="0" maxOccurs="unbounded">
          <xs:element ref="engine:VisualElement" />
        </xs:sequence>
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="false" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="BuilderSelectionIndicator" substitutionGroup="engine:VisualElement" xmlns:q27="Unity.UI.Builder" type="q27:BuilderSelectionIndicatorType" />
  <xs:complexType name="CheckerboardBackgroundType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:sequence minOccurs="0" maxOccurs="unbounded">
          <xs:element ref="engine:VisualElement" />
        </xs:sequence>
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Ignore" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="false" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="CheckerboardBackground" substitutionGroup="engine:VisualElement" xmlns:q28="Unity.UI.Builder" type="q28:CheckerboardBackgroundType" />
  <xs:complexType name="TransitionsListViewType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:sequence minOccurs="0" maxOccurs="unbounded">
          <xs:element ref="engine:VisualElement" />
        </xs:sequence>
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="false" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="TransitionsListView" substitutionGroup="engine:VisualElement" xmlns:q29="Unity.UI.Builder" type="q29:TransitionsListViewType" />
  <xs:complexType name="BuilderParentTrackerType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:sequence minOccurs="0" maxOccurs="unbounded">
          <xs:element ref="engine:VisualElement" />
        </xs:sequence>
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="false" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="BuilderParentTracker" substitutionGroup="engine:VisualElement" xmlns:q30="Unity.UI.Builder" type="q30:BuilderParentTrackerType" />
  <xs:simpleType name="BuilderAttributesTestElement_enum-attr_Type">
    <xs:restriction base="xs:string">
      <xs:enumeration value="None" />
      <xs:enumeration value="Good" />
      <xs:enumeration value="Bad" />
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="BuilderAttributesTestElementType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="false" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:attribute default="default_value" name="string-attr" type="xs:string" use="optional" />
        <xs:attribute default="0.1" name="float-attr" type="xs:float" use="optional" />
        <xs:attribute default="0.1" name="double-attr" type="xs:double" use="optional" />
        <xs:attribute default="2" name="int-attr" type="xs:int" use="optional" />
        <xs:attribute default="3" name="long-attr" type="xs:long" use="optional" />
        <xs:attribute default="false" name="bool-attr" type="xs:boolean" use="optional" />
        <xs:attribute default="RGBA(1.000, 0.000, 0.000, 1.000)" name="color-attr" type="xs:string" use="optional" />
        <xs:attribute default="Bad" name="enum-attr" xmlns:q31="Unity.UI.Builder" type="q31:BuilderAttributesTestElement_enum-attr_Type" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="BuilderAttributesTestElement" substitutionGroup="engine:VisualElement" xmlns:q32="Unity.UI.Builder" type="q32:BuilderAttributesTestElementType" />
  <xs:complexType name="DimensionStyleFieldType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:sequence minOccurs="0" maxOccurs="unbounded">
          <xs:element ref="engine:VisualElement" />
        </xs:sequence>
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="true" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:attribute default="" name="binding-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="label" type="xs:string" use="optional" />
        <xs:attribute default="true" name="show-options" type="xs:boolean" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="DimensionStyleField" substitutionGroup="engine:VisualElement" xmlns:q33="Unity.UI.Builder" type="q33:DimensionStyleFieldType" />
  <xs:complexType name="FieldStatusIndicatorType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:sequence minOccurs="0" maxOccurs="unbounded">
          <xs:element ref="engine:VisualElement" />
        </xs:sequence>
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="false" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:attribute default="" name="field-name" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="FieldStatusIndicator" substitutionGroup="engine:VisualElement" xmlns:q34="Unity.UI.Builder" type="q34:FieldStatusIndicatorType" />
  <xs:complexType name="BuilderResizerType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:sequence minOccurs="0" maxOccurs="unbounded">
          <xs:element ref="engine:VisualElement" />
        </xs:sequence>
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="false" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="BuilderResizer" substitutionGroup="engine:VisualElement" xmlns:q35="Unity.UI.Builder" type="q35:BuilderResizerType" />
  <xs:complexType name="FoldoutFieldType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="false" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:attribute default="" name="binding-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="text" type="xs:string" use="optional" />
        <xs:attribute default="" name="binding-paths" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="FoldoutField" substitutionGroup="engine:VisualElement" xmlns:q36="Unity.UI.Builder" type="q36:FoldoutFieldType" />
  <xs:complexType name="BuilderPlacementIndicatorType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:sequence minOccurs="0" maxOccurs="unbounded">
          <xs:element ref="engine:VisualElement" />
        </xs:sequence>
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="false" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="BuilderPlacementIndicator" substitutionGroup="engine:VisualElement" xmlns:q37="Unity.UI.Builder" type="q37:BuilderPlacementIndicatorType" />
  <xs:complexType name="BuilderPaneType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="true" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:attribute default="" name="title" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="BuilderPane" substitutionGroup="engine:VisualElement" xmlns:q38="Unity.UI.Builder" type="q38:BuilderPaneType" />
  <xs:complexType name="FoldoutTransitionFieldType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="false" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:attribute default="" name="binding-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="text" type="xs:string" use="optional" />
        <xs:attribute default="" name="binding-paths" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="FoldoutTransitionField" substitutionGroup="engine:VisualElement" xmlns:q39="Unity.UI.Builder" type="q39:FoldoutTransitionFieldType" />
  <xs:complexType name="ToggleButtonStripType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:sequence minOccurs="0" maxOccurs="unbounded">
          <xs:element ref="engine:VisualElement" />
        </xs:sequence>
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="true" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:attribute default="" name="binding-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="label" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="ToggleButtonStrip" substitutionGroup="engine:VisualElement" xmlns:q40="Unity.UI.Builder" type="q40:ToggleButtonStripType" />
  <xs:complexType name="TransformOriginStyleFieldType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:sequence minOccurs="0" maxOccurs="unbounded">
          <xs:element ref="engine:VisualElement" />
        </xs:sequence>
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="true" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:attribute default="" name="binding-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="label" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="TransformOriginStyleField" substitutionGroup="engine:VisualElement" xmlns:q41="Unity.UI.Builder" type="q41:TransformOriginStyleFieldType" />
  <xs:complexType name="BuilderAnchorerType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:sequence minOccurs="0" maxOccurs="unbounded">
          <xs:element ref="engine:VisualElement" />
        </xs:sequence>
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="false" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="BuilderAnchorer" substitutionGroup="engine:VisualElement" xmlns:q42="Unity.UI.Builder" type="q42:BuilderAnchorerType" />
  <xs:complexType name="ScaleStyleFieldType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:sequence minOccurs="0" maxOccurs="unbounded">
          <xs:element ref="engine:VisualElement" />
        </xs:sequence>
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="true" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:attribute default="" name="binding-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="label" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="ScaleStyleField" substitutionGroup="engine:VisualElement" xmlns:q43="Unity.UI.Builder" type="q43:ScaleStyleFieldType" />
  <xs:complexType name="IntegerStyleFieldType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:sequence minOccurs="0" maxOccurs="unbounded">
          <xs:element ref="engine:VisualElement" />
        </xs:sequence>
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="true" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:attribute default="" name="binding-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="label" type="xs:string" use="optional" />
        <xs:attribute default="true" name="show-options" type="xs:boolean" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="IntegerStyleField" substitutionGroup="engine:VisualElement" xmlns:q44="Unity.UI.Builder" type="q44:IntegerStyleFieldType" />
  <xs:complexType name="TextAlignStripType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:sequence minOccurs="0" maxOccurs="unbounded">
          <xs:element ref="engine:VisualElement" />
        </xs:sequence>
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="true" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:attribute default="" name="binding-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="label" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="TextAlignStrip" substitutionGroup="engine:VisualElement" xmlns:q45="Unity.UI.Builder" type="q45:TextAlignStripType" />
</xs:schema>