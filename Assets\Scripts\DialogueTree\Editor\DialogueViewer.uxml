<ui:UXML xmlns:ui="UnityEngine.UIElements" xmlns:uie="UnityEditor.UIElements" xsi="http://www.w3.org/2001/XMLSchema-instance" engine="UnityEngine.UIElements" editor="UnityEditor.UIElements" noNamespaceSchemaLocation="../../../UIElementsSchema/UIElements.xsd" editor-extension-mode="False">
    <Style src="project://database/Assets/Scripts/DialogueTree/Editor/DialogueViewer.uss?fileID=7433441132597879392&amp;guid=e58debb45e2f3354baf0d421cbc1034a&amp;type=3#DialogueViewer" />
    <ui:VisualElement style="flex-grow: 1; flex-direction: row; justify-content: space-around; align-self: stretch;">
        <ui:VisualElement name="Left-View" style="flex-grow: 0.16; width: auto; min-width: 210px; flex-shrink: 1; border-right-color: rgba(0, 0, 0, 0.82); border-right-width: 1px; flex-basis: auto; max-width: none; align-items: stretch; align-self: auto; justify-content: flex-start;">
            <ui:VisualElement style="flex-grow: 0; height: 16px;">
                <uie:Toolbar style="align-items: auto; align-self: auto; height: 16px;" />
            </ui:VisualElement>
            <ui:Label tabindex="-1" text="Inspector" parse-escape-sequences="true" display-tooltip-when-elided="true" style="align-items: auto; justify-content: space-around; align-self: auto; -unity-text-align: middle-center; -unity-font-style: normal; -unity-text-outline-color: rgba(0, 0, 0, 0.82); text-shadow: 3px 3px 1px rgba(0, 0, 0, 0.82); -unity-text-outline-width: 0; height: 16px; font-size: 14px; letter-spacing: 0; word-spacing: 0; -unity-paragraph-spacing: 0; flex-shrink: 0; flex-grow: 0;" />
            <ViewDialogueInspector style="flex-grow: 1;" />
        </ui:VisualElement>
        <ui:VisualElement name="Right-View" style="flex-grow: 1;">
            <ViewDialogueTree focusable="true" style="flex-grow: 1;">
                <ui:VisualElement style="flex-grow: 0; height: 18px; align-items: center; justify-content: flex-start; align-self: stretch; border-top-width: 2px; border-bottom-width: 2px; border-bottom-color: rgba(0, 0, 0, 0); flex-shrink: 0; flex-direction: row;">
                    <ui:Foldout text="Foldout" value="true" style="flex-grow: 0; height: 16px; justify-content: center;">
                        <ui:Button text="刷新视图" parse-escape-sequences="true" display-tooltip-when-elided="true" name="RefreshView" style="flex-shrink: 0; flex-grow: 0; min-height: 18px; min-width: 38px; max-width: 68px; max-height: 18px; height: 18px; width: 68px; -unity-font-style: normal; font-size: 14px; -unity-font: initial;" />
                        <ui:Button text="垂直排列" parse-escape-sequences="true" display-tooltip-when-elided="true" name="VerticalSorting" style="flex-shrink: 0; flex-grow: 0; min-height: 18px; min-width: 38px; max-width: 68px; max-height: 18px; height: 18px; width: 68px; -unity-font-style: normal; font-size: 14px;" />
                        <ui:Button text="迷你地图" parse-escape-sequences="true" display-tooltip-when-elided="true" name="MiniMap" style="flex-shrink: 0; flex-grow: 0; min-height: 18px; min-width: 38px; max-width: 68px; max-height: 18px; height: 18px; width: 68px; -unity-font-style: normal; font-size: 14px;" />
                    </ui:Foldout>
                    <ui:Label tabindex="-1" text="DH10101" parse-escape-sequences="true" display-tooltip-when-elided="true" name="DialogueTreeID" style="right: 0; position: absolute; font-size: 16px; -unity-font-style: bold; text-shadow: 5px 5px 2px rgba(0, 0, 0, 0.82); -unity-text-align: middle-center; height: 0; justify-content: center; border-right-width: 2px; flex-shrink: 1; flex-grow: 1; top: auto; bottom: auto;" />
                </ui:VisualElement>
            </ViewDialogueTree>
        </ui:VisualElement>
    </ui:VisualElement>
</ui:UXML>
