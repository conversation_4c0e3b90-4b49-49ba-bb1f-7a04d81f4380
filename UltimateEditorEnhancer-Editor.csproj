﻿<Project>
  <!-- Generated file, do not modify, your changes will be overwritten (use AssetPostprocessor.OnGeneratedCSProject) -->
  <PropertyGroup>
    <BaseIntermediateOutputPath>Temp\obj\$(MSBuildProjectName)</BaseIntermediateOutputPath>
    <IntermediateOutputPath>$(BaseIntermediateOutputPath)</IntermediateOutputPath>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <UseCommonOutputDirectory>true</UseCommonOutputDirectory>
    <OutputPath>Temp\bin\Debug\</OutputPath>
  </PropertyGroup>
  <Import Project="Sdk.props" Sdk="Microsoft.NET.Sdk" />
  <ItemGroup>
    <ProjectCapability Include="Unity" />
  </ItemGroup>
  <PropertyGroup>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <EnableDefaultItems>false</EnableDefaultItems>
    <LangVersion>9.0</LangVersion>
    <RootNamespace></RootNamespace>
    <OutputType>Library</OutputType>
    <AssemblyName>UltimateEditorEnhancer-Editor</AssemblyName>
    <TargetFramework>netstandard2.1</TargetFramework>
    <BaseDirectory>.</BaseDirectory>
  </PropertyGroup>
  <PropertyGroup>
    <NoWarn>0169;USG0001</NoWarn>
    <DefineConstants>UNITY_2022_3_33;UNITY_2022_3;UNITY_2022;UNITY_5_3_OR_NEWER;UNITY_5_4_OR_NEWER;UNITY_5_5_OR_NEWER;UNITY_5_6_OR_NEWER;UNITY_2017_1_OR_NEWER;UNITY_2017_2_OR_NEWER;UNITY_2017_3_OR_NEWER;UNITY_2017_4_OR_NEWER;UNITY_2018_1_OR_NEWER;UNITY_2018_2_OR_NEWER;UNITY_2018_3_OR_NEWER;UNITY_2018_4_OR_NEWER;UNITY_2019_1_OR_NEWER;UNITY_2019_2_OR_NEWER;UNITY_2019_3_OR_NEWER;UNITY_2019_4_OR_NEWER;UNITY_2020_1_OR_NEWER;UNITY_2020_2_OR_NEWER;UNITY_2020_3_OR_NEWER;UNITY_2021_1_OR_NEWER;UNITY_2021_2_OR_NEWER;UNITY_2021_3_OR_NEWER;UNITY_2022_1_OR_NEWER;UNITY_2022_2_OR_NEWER;UNITY_2022_3_OR_NEWER;PLATFORM_ARCH_64;UNITY_64;UNITY_INCLUDE_TESTS;ENABLE_AR;ENABLE_AUDIO;ENABLE_CACHING;ENABLE_CLOTH;ENABLE_EVENT_QUEUE;ENABLE_MICROPHONE;ENABLE_MULTIPLE_DISPLAYS;ENABLE_PHYSICS;ENABLE_TEXTURE_STREAMING;ENABLE_VIRTUALTEXTURING;ENABLE_LZMA;ENABLE_UNITYEVENTS;ENABLE_VR;ENABLE_WEBCAM;ENABLE_UNITYWEBREQUEST;ENABLE_WWW;ENABLE_CLOUD_SERVICES;ENABLE_CLOUD_SERVICES_ADS;ENABLE_CLOUD_SERVICES_USE_WEBREQUEST;ENABLE_CLOUD_SERVICES_CRASH_REPORTING;ENABLE_CLOUD_SERVICES_PURCHASING;ENABLE_CLOUD_SERVICES_ANALYTICS;ENABLE_CLOUD_SERVICES_BUILD;ENABLE_EDITOR_GAME_SERVICES;ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT;ENABLE_CLOUD_LICENSE;ENABLE_EDITOR_HUB_LICENSE;ENABLE_WEBSOCKET_CLIENT;ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API;ENABLE_DIRECTOR_AUDIO;ENABLE_DIRECTOR_TEXTURE;ENABLE_MANAGED_JOBS;ENABLE_MANAGED_TRANSFORM_JOBS;ENABLE_MANAGED_ANIMATION_JOBS;ENABLE_MANAGED_AUDIO_JOBS;ENABLE_MANAGED_UNITYTLS;INCLUDE_DYNAMIC_GI;ENABLE_SCRIPTING_GC_WBARRIERS;PLATFORM_SUPPORTS_MONO;RENDER_SOFTWARE_CURSOR;ENABLE_VIDEO;ENABLE_ACCELERATOR_CLIENT_DEBUGGING;ENABLE_NAVIGATION_PACKAGE_DEBUG_VISUALIZATION;ENABLE_NAVIGATION_HEIGHTMESH_RUNTIME_SUPPORT;ENABLE_NAVIGATION_UI_REQUIRES_PACKAGE;PLATFORM_STANDALONE;TEXTCORE_1_0_OR_NEWER;PLATFORM_STANDALONE_WIN;UNITY_STANDALONE_WIN;UNITY_STANDALONE;UNITY_UGP_API;ENABLE_RUNTIME_GI;ENABLE_MOVIES;ENABLE_NETWORK;ENABLE_NVIDIA;ENABLE_CRUNCH_TEXTURE_COMPRESSION;ENABLE_OUT_OF_PROCESS_CRASH_HANDLER;ENABLE_CLUSTER_SYNC;ENABLE_CLUSTERINPUT;PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP;GFXDEVICE_WAITFOREVENT_MESSAGEPUMP;PLATFORM_INITIALIZES_MEMORY_MANAGER_EXPLICITLY;ENABLE_MONO;NET_4_6;NET_UNITY_4_8;ENABLE_PROFILER;DEBUG;TRACE;UNITY_ASSERTIONS;UNITY_EDITOR;UNITY_EDITOR_64;UNITY_EDITOR_WIN;ENABLE_UNITY_COLLECTIONS_CHECKS;ENABLE_BURST_AOT;UNITY_TEAM_LICENSE;ENABLE_CUSTOM_RENDER_TEXTURE;ENABLE_DIRECTOR;ENABLE_LOCALIZATION;ENABLE_SPRITES;ENABLE_TERRAIN;ENABLE_TILEMAP;ENABLE_TIMELINE;ENABLE_LEGACY_INPUT_MANAGER;TEXTCORE_FONT_ENGINE_1_5_OR_NEWER;UEE;DOTWEEN;CSHARP_7_OR_LATER;CSHARP_7_3_OR_NEWER;UNITY_EDITOR_ONLY_COMPILATION</DefineConstants>
    <AllowUnsafeBlocks>False</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup>
    <NoStandardLibraries>true</NoStandardLibraries>
    <NoStdLib>true</NoStdLib>
    <NoConfig>true</NoConfig>
    <DisableImplicitFrameworkReferences>true</DisableImplicitFrameworkReferences>
    <MSBuildWarningsAsMessages>MSB3277</MSBuildWarningsAsMessages>
  </PropertyGroup>
  <PropertyGroup>
    <UnityProjectGenerator>Package</UnityProjectGenerator>
    <UnityProjectGeneratorVersion>2.0.23</UnityProjectGeneratorVersion>
    <UnityProjectGeneratorStyle>SDK</UnityProjectGeneratorStyle>
    <UnityProjectType>Editor:5</UnityProjectType>
    <UnityBuildTarget>StandaloneWindows64:19</UnityBuildTarget>
    <UnityVersion>2022.3.33f1c1</UnityVersion>
  </PropertyGroup>
  <ItemGroup>
    <Analyzer Include="C:\Users\<USER>\.vscode\extensions\visualstudiotoolsforunity.vstuc-1.1.3\Analyzers\Microsoft.Unity.Analyzers.dll" />
    <Analyzer Include="D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Tools\Unity.SourceGenerators\Unity.SourceGenerators.dll" />
    <Analyzer Include="D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Tools\Unity.SourceGenerators\Unity.Properties.SourceGenerator.dll" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Integration\FullscreenEditor.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\ComponentHeaderItems\RuntimeSaveButton.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Prefs\Actions.Prefs.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Behaviours\DragAndDropToTab.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Toolbar\Windows\ToolbarWindows.OpenedProvider.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Editors\ViewStateReadmeEditor.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Hierarchy\BestIconDrawer.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Context Menu\Layouts\WindowsLayout.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Hierarchy\SoloPickability.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Hierarchy\BookmarkButton.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Scene View\FloatToolbars\FloatToolbar.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Prefs\ImproveBehaviours.Prefs.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\UnityTypes\TextureImporterInspectorRef.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Windows\EditorIconsBrowser.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Hierarchy\TreeDrawer.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Scene View\SelectionSize.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Scene View\RotateByShortcut.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Context Menu\Actions\SceneViewActions.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Prefs\DuplicateTool.Prefs.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Helpers\SceneManagerHelper.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Hierarchy\BackgroundRule.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Prefs\CreateBrowser.Prefs.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Windows\SceneHistory\SceneHistoryItem.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Behaviours\SelectPrefabInstances.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Helpers\PositionHandle.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Core\Version.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\ComponentHeaderItems\ComponentBookmark.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Prefs\PrefManager.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Prefs\PopupWindows.Prefs.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Prefs\Project.Prefs.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Windows\GettingStarted.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Toolbar\Windows\ToolbarWindows.MiniLayouts.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Interfaces\IHasShortcutPref.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\UnityTypes\EditorToolUtilityRef.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Helpers\RecordUpgrader.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Helpers\GenericMenuEx.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Attributes\TitleAttribute.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\UnityTypes\EditorGUILayoutEx.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Project\ProjectCreateMaterialFromTexture.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Project\FileExtensions.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Scene View\QuickAccessBar\Actions\SaveAction.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Prefs\FavoriteWindows.Prefs.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Helpers\HierarchyHelper.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Editors\MissedScriptEditor.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\UnityTypes\CurveEditorWindowRef.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Managers\ToolbarManager.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Prefs\ObjectToolbar.Prefs.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Enums\CreateBrowserTarget.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Helpers\GenericMenuItem.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Helpers\ComponentHeaderButtonOrder.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Interceptors\TransformInspectorInterceptor.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Prefs\RemoveIconPrefix.Prefs.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Enums\NestedEditorSide.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Helpers\HierarchyToolOrder.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\UnityTypes\AssetPreviewRef.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Integration\EnhancedHierarchy.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\UnityTypes\GUILayoutUtilityRef.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\UnityTypes\ShortcutIntegrationRef.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Toolbar\Timer.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Prefs\Unsafe.Prefs.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Context Menu\PopupWindows\Inspector.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Project\OddEven.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Scene View\HighJumpToPoint.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\UnityTypes\ConsoleWindowRef.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\UnityTypes\EditorRef.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Prefs\ProjectFolderIconManager.Prefs.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Managers\SceneViewManager.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Project\ProjectCreateCustomEditor.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Prefs\Rename.Prefs.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Hierarchy\ComponentIconDrawer.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\UnityTypes\ShowModeRef.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Inspector\ComponentExporter.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Prefs\Updater.Prefs.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\UnityTypes\IMGUIContainerRef.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Attributes\RuntimeOnlyAttribute.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Managers\BindingManager.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Helpers\TempContent.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Behaviours\AddComponentBehavior.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Scene View\SmartSelection.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Helpers\CanvasUtils.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Interceptors\TreeViewControllerUserInputChangedExpandedState.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Windows\CheckIntegrityWindow.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Hierarchy\ErrorDrawer.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Enums\BackgroundCondition.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Prefs\Bookmarks.Prefs.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Windows\CreateBrowser\CreateBrowser.PrefabProvider.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Prefs\SelectionBounds.Prefs.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Windows\FlatSelectorWindow.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Enums\HeaderCondition.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Hierarchy\OddEven.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Prefs\GeneralManagers.Prefs.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Project\ProjectCreateMaterial.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\UnityTypes\SceneHierarchyWindowRef.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Inspector\ObjectFieldDragAndDrop.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Enums\ButtonEvent.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Behaviours\Rename.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Managers\ReferenceManager.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Helpers\FixedIDs.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Toolbar\Windows\ToolbarWindows.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Prefs\ObjectPlacer.Prefs.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Windows\CreateBrowser\CreateBrowser.Provider.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Interceptors\PropertyHandlerInterceptor.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Windows\PinAndClose.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\UnityTypes\PropertyEditorRef.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Windows\CreateBrowser\CreateBrowser.Item.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Context Menu\Layouts\ActionsLayout.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\UnityTypes\AssetsTreeViewDataSourceRef.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\PostHeaderItems\PostHeaderItem.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Helpers\SceneReferencesLoader.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Editors\LightEditorExt.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Windows\Search\Search.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Windows\InputDialog.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Project\ProjectCreateFolder.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Prefs\JumpToPoint.Prefs.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Scene View\QuickAccessBar\Actions\OpenAction.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Behaviours\Replace.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Inspector\ComponentEditor.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Managers\InputManager.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Project\ProjectItem.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Prefs\Backgrounds.Prefs.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Windows\CreateBrowser\CreateBrowser.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\UnityTypes\SceneVisibilityManagerRef.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Scene View\JumpToPoint.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\UnityTypes\ObjectListAreaRef.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Scene View\QuickAccessBar\Actions\QuickAccessAction.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Tools\CustomPivotRotation.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\PostHeaderItems\HeaderBookmark.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Behaviours\CollectionSelector.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Windows\ViewGallery\ViewGallery.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Improvements\CurveEditorWindowImprovement.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\UnityTypes\ActiveEditorTrackerRef.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\UnityTypes\UnityEventDrawerRef.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Attributes\ComponentHeaderButtonAttribute.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Context Menu\EditorMenu.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Windows\CreateBrowser\CreateBrowser.CreateItem.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Windows\About.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Enums\ToolbarAlign.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Context Menu\Layouts\LayoutItem.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Toolbar\Windows\ToolbarWindows.Provider.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Windows\GameObjectHierarchySettings.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Context Menu\Actions\CloseComponentWindows.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Windows\CreateBrowser\CreateBrowser.PrefabItem.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\UnityTypes\AddComponentWindowRef.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Context Menu\Actions\MaximizeSceneView.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Interceptors\NumberFieldInterceptor.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\UnityTypes\PropertyHandlerRef.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Interceptors\DrawEditorHeaderItemsInterceptor.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Helpers\StringHelper.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\UnityTypes\SearchFilterRef.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Scene View\DropToFloor.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\UnityTypes\GameViewRef.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Windows\SceneBackups.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\UnityTypes\ObjectListAreaStateRef.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Prefs\Main.Prefs.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\UnityTypes\AudioUtilsRef.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Helpers\SceneViewHelper.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Managers\LogManager.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Windows\TransformEditorWindow.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\PostHeaderItems\NoteItem.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\UnityTypes\EditorWindowRef.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Hierarchy\IconSelector.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Helpers\LayoutHelper.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\UnityTypes\EventRef.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Windows\Search\Search.UI.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Windows\Updater.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\UnityTypes\EditorGUINumberFieldValueRef.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Behaviours\Ungroup.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Behaviours\DragAndDropBehavior.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Helpers\ComponentUtils.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Helpers\GlobalObjectIdHelper.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\UnityTypes\SceneVisibilityStateRef.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Managers\NoteManager.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\UnityTypes\ContainerWindowRef.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Prefs\Replace.Prefs.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\UnityTypes\ComponentUtilityRef.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Toolbar\Windows\ToolbarWindows.RecentProvider.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Prefs\Hierarchy.Prefs.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Prefs\FrameSelectedBounds.Prefs.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Context Menu\Layouts\MainLayoutItem.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Windows\FlatSmartSelectionWindow.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Windows\ObjectWindow.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\ComponentHeaderItems\TransformInspectorGlobalValues.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Prefs\DistanceTool.Prefs.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Attributes\RequireComponentsAttribute.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\UnityTypes\PrefabImporterRef.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Windows\TimescaleWindow.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\UnityTypes\GUIContentRef.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Attributes\RequireSelectedAttribute.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Scene View\QuickAccessBar\QuickAccessItem.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Interceptors\Base\StatedInterceptor.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Windows\Search\Search.GameObjectRecord.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Windows\Shortcuts.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Attributes\HideInIntegrityAttribute.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\UnityTypes\IWindowBackendRef.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Windows\Search\Search.ProjectRecord.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Helpers\ProjectToolOrder.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Helpers\MenuItemOrder.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Windows\Bookmarks\Bookmarks.RowDrawer.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Inspector\EmptyInspector.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Scene View\QuickAccessBar\QuickAccess.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Windows\PopupWindow.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Helpers\GameObjectUtils.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\TransformEditorTools\AlignTool.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Context Menu\Layouts\MainLayoutItemGeneric.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\UnityTypes\ProjectBrowserRef.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\UnityTypes\RecycledTextEditorRef.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Behaviours\AutoSave.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Project\ProjectFolderRule.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\UnityTypes\SpriteUtilityRef.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\UnityTypes\HostViewRef.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Managers\WindowManager.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Prefs\Toolbar.Prefs.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Prefs\ContextMenu.Prefs.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\UnityTypes\TerrainInspectorRef.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Project\ProjectItemDrawer.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Prefs\SceneReferences.Prefs.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Windows\ComponentWindow.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Scene View\FloatToolbars\FloatToolbarManager.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Managers\SelectionBoundsManager.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Toolbar\Windows\ToolbarWindows.WindowRecord.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Hierarchy\HeaderRule.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Attributes\ValidateAttribute.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Helpers\StringWithNumberComparer.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Behaviours\CreateDirectory.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Scene View\TerrainBrushSize.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Attributes\RequireMultipleGameObjectsAttribute.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Scene View\ObjectPlacer.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\UnityTypes\EditorUtilityRef.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Prefs\GameObject.Prefs.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\CustomPropertyDrawers\UnityEventBaseDrawer.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Managers\LocalSettings.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Prefs\Preview.Prefs.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Prefs\Window.Prefs.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\UnityTypes\TreeViewControllerRef.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Prefs\ScriptingDefineSymbols.Prefs.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Prefs\SwitchCustomTool.Prefs.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Managers\SelectionManager.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Windows\Search\Search.Cache.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Windows\Bookmarks\ProjectFolderBookmark.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Inspector\InspectorBar.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Prefs\Group.Prefs.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Windows\SceneHistory\SceneHistory.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Context Menu\Layouts\BreadcrumbsLayout.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Behaviours\MaximizeGameView.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Scene View\ToolValues.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Context Menu\PopupWindows\Hierarchy.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Helpers\Icons.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\UnityTypes\LogEntriesRef.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Scene View\Highlighter.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Core\Log.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Helpers\ResourcesCache.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Enums\HierarchyIconsDisplayRule.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Context Menu\Actions\Group.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Behaviours\DragAndDropToEventField.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Context Menu\PopupWindows\PopupWindowItem.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Context Menu\Actions\AddComponent.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Scene View\FloatToolbars\ObjectToolbar.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Interceptors\EditorGUIDoTextFieldInterceptor.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Hierarchy\BackgroundDrawer.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Prefs\Order.Prefs.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Tools\DuplicateTool.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Prefs\ToolValues.Prefs.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\CustomPropertyDrawers\EmptyInspectorItemDrawer.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Project\ProjectFolderIconDrawer.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Prefs\Log.Prefs.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Prefs\RotateByShortcut.Prefs.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Enums\SceneViewVisibleRules.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Managers\GlobalEventManager.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\UnityTypes\SearchableEditorWindowRef.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Prefs\Shortcut.Prefs.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Prefs\Methods.Prefs.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Interceptors\CurveEditorWindowGetCurveEditorInterceptor.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Enums\QuickAccessItemType.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Managers\KeyManager.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\UnityTypes\AnimationUtilityRef.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Helpers\SerializedPropertyHelper.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Context Menu\PopupWindows\Bookmarks.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Windows\CreateBrowser\CreateBrowser.BookmarkProvider.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Helpers\EditorIconContents.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Interceptors\AnimatorInspectorInterceptor.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Windows\ViewGallery\ViewGallery.ViewStateItem.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Behaviours\Group.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Prefs\Breadcrumbs.Prefs.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Windows\DistanceTool.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\UnityTypes\PrefabUtilityRef.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Windows\ViewGallery\ViewGallery.CameraStateItem.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\UnityTypes\ProjectWindowUtilsRef.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Windows\Welcome.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Context Menu\Actions\Snapping.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Helpers\Links.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Context Menu\Actions\History.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Prefs\MiniLayouts.Prefs.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Context Menu\Actions\Replace.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Interfaces\IValidatableLayoutItem.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Prefs\ToolbarWindows.Prefs.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Project\ProjectCreateShader.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Windows\ViewGallery\SelectionViewStates.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Helpers\MathHelper.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Scene View\MoveToPoint.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Hierarchy\Header.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Helpers\VisualElementHelper.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Prefs\StandalonePrefManager.Prefs.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\UnityTypes\Compatibility.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Managers\EventManager.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Integration\ProGrids.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Windows\LongTextEditorWindow.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Toolbar\Windows\ToolbarWindows.FavoriteProvider.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\UnityTypes\HandleUtilityRef.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\UnityTypes\SceneHierarchyRef.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Enums\TimerMode.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Prefs\TerrainBrushSize.Prefs.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Scene View\Waila.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Interceptors\EditorGUIDoObjectFieldInterceptor.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Editors\DocumentationEditor.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\UnityTypes\EditorElementRef.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\UnityTypes\GUIViewRef.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\UnityTypes\PaintTreesToolRef.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Helpers\EditorResources.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\PostHeaderItems\HeaderFieldFilter.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Interceptors\EnumPopupInterceptor.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Helpers\TemporaryObjectManager.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\UnityTypes\GUISkinRef.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Interfaces\IInvokableLayoutItem.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Hierarchy\RowEnable.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Managers\HierarchyItemDrawer.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Context Menu\PopupWindows\FavoriteWindows.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Windows\MigrationWindow.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Prefs\SettingsToolbar.Prefs.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\UnityTypes\DockAreaRef.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\PostHeaderItems\HeaderNote.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Prefs\BackupScene.Prefs.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Windows\Search\Search.WindowRecord.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Context Menu\Actions\ShowSettings.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Enums\SmartSelectionStyle.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Interceptors\GenericInterceptor.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Prefs\Waila.Prefs.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Windows\LayoutWindow.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Integration\Cinemachine.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Inspector\InspectorInjector.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Enums\QuickAccessItemIcon.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Prefs\Inspector.Prefs.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Interceptors\Base\GenericInterceptor.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\PostHeaderItems\PostHeaderManager.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Windows\AdvancedDropToFloor.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Scene View\SceneViewAlignDrawer.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Windows\FavoriteWindow\FavoriteWindowItem.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\UnityTypes\GUILayoutGroupRef.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Windows\Search\Search.Record.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Project\ProjectCreateScript.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Windows\AutoSizePopupWindow.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Project\ProjectPlayAudio.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\UnityTypes\GridSettingsRef.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Inspector\ObjectFieldSelector.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Enums\QuickAccessWindowMode.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Interceptors\HelpIconButtonInterceptor.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\ComponentHeaderItems\CopyPaste.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\UnityTypes\EditorGUIUtilityRef.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Context Menu\Actions\ActionItem.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\UnityTypes\ProjectSettingsWindowRef.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Tools\PivotTool.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Prefs\ZoomShortcut.Prefs.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Helpers\SnapHelper.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\UnityTypes\TextEditorRef.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Hierarchy\NoteDrawer.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\UnityTypes\AssetImporterEditorRef.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Helpers\PackageLocator.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Prefs\Autosave.Prefs.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Helpers\ProjectAssetCache.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Interceptors\HierarchyToolbarInterceptor.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\UnityTypes\AnimatorInspectorRef.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\UnityTypes\PrefabStageUtilityRef.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Windows\ViewGallery\ViewGallery.ViewItem.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Context Menu\PopupWindows\Components.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Prefs\Ungroup.Prefs.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Inspector\NestedEditor.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Windows\Search\Search.ComponentRecord.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Interfaces\IStateablePref.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Inspector\AnimatorInspectorClips.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Behaviours\LODGroupSelector.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Prefs\Highlight.Prefs.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Context Menu\Actions\SelectGameObject.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\UnityTypes\ITreeViewDataSourceRef.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Windows\Bookmarks\Bookmarks.GridDrawer.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Windows\CreateBrowser\CreateBrowser.PrefabItemFolder.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Helpers\WindowsHelper.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\ComponentHeaderItems\AlignAndDistribute.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\UnityTypes\IconSelectorRef.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Prefs\QuickAccessBar.Prefs.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\TransformEditorTools\TransformEditorTool.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Helpers\RenderPipelineHelper.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Prefs\SelectionHistory.Prefs.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\UnityTypes\ViewRef.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\UnityTypes\ToolbarRef.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Prefs\Search.Prefs.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Core\Settings.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Helpers\Styles.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Helpers\OddEvenRowDrawer.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\UnityTypes\EditorApplicationRef.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Prefs\DropToFloor.Prefs.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Interceptors\CurveEditorWindowOnGUIInterceptor.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Context Menu\Actions\Create.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\UnityTypes\PresetSelectorRef.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Hierarchy\Highlighter.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\UnityTypes\ReorderableListRef.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\UnityTypes\ObjectSelectorRef.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\UnityTypes\TransformInspectorRef.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Scene View\Preview.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Scene View\FrameSelectedBounds.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\ComponentHeaderItems\CameraAlignWith.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Hierarchy\SoloVisibility.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\UnityTypes\TreeViewGUIRef.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Helpers\SceneViewOrder.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Prefs\ViewGallery.Prefs.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Behaviours\Switcher.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\ComponentHeaderItems\MoveComponentAboveBelow.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Inspector\LongTextEditor.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Helpers\Resources.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Context Menu\Actions\UpdateAvailable.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\CustomPropertyDrawers\ObjectFieldDrawer.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Helpers\GUILayoutUtils.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\ComponentHeaderItems\BoxColliderDetectSize.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Enums\AutoSize.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Windows\CreateBrowser\CreateBrowser.CreateProvider.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Behaviours\SelectionHistory.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Interceptors\DrawPresetButtonInterceptor.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Scene View\ZoomShortcutBehaviour.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Windows\NoteManager.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Prefs\Headers.Prefs.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Windows\Bookmarks\Bookmarks.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Prefs\EmptyInspector.Prefs.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\TransformEditorTools\BoundsTool.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Core\ComponentHeaderContextMenuInjection.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Managers\HierarchyItem.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Prefs\SceneView.Prefs.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Prefs\Navigation.Prefs.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\UnityTypes\PopupCallbackInfoRef.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Windows\CreateBrowser\CreateBrowser.CreateItemFolder.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Helpers\DelegateHelper.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\UnityTypes\PlayModeViewRef.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Interceptors\Base\Interceptor.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Inspector\TransformAlign.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\UnityTypes\InspectorWindowRef.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Prefs\Switcher.Prefs.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Interceptors\ObjectFieldInterceptor.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Helpers\StyleSheets.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Core\CompilerDefine.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\UnityTypes\EditorGUIRef.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Behaviours\SelectCustomTool.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Context Menu\PopupWindows\Project.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\UnityTypes\SelectionRef.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Context Menu\Actions\Align.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Scene View\ViewStateDrawer.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Windows\CreateBrowser\CreateBrowser.FolderItem.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Interceptors\ReorderableListInterceptor.cs" />
    <Compile Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\UnityTypes\LogEntryRef.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Scripts\Editor\Ultimate Editor Enhancer-Editor.asmdef" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="UnityEngine">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AIModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.AIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ARModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.ARModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AccessibilityModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.AccessibilityModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AndroidJNIModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.AndroidJNIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AnimationModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.AnimationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AssetBundleModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.AssetBundleModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AudioModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.AudioModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ClothModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.ClothModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ClusterInputModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.ClusterInputModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ClusterRendererModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.ClusterRendererModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ContentLoadModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.ContentLoadModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.CoreModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.CoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.CrashReportingModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.CrashReportingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.DSPGraphModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.DSPGraphModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.DirectorModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.DirectorModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GIModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.GIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GameCenterModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.GameCenterModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GridModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.GridModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.HotReloadModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.HotReloadModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.IMGUIModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.IMGUIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ImageConversionModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.ImageConversionModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.InputModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputLegacyModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.InputLegacyModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.JSONSerializeModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.JSONSerializeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.LocalizationModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.LocalizationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.NVIDIAModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.NVIDIAModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ParticleSystemModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.ParticleSystemModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PerformanceReportingModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.PerformanceReportingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PhysicsModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.PhysicsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.Physics2DModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.Physics2DModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ProfilerModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.ProfilerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PropertiesModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.PropertiesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ScreenCaptureModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.ScreenCaptureModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SharedInternalsModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.SharedInternalsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SpriteMaskModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.SpriteMaskModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SpriteShapeModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.SpriteShapeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.StreamingModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.StreamingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SubstanceModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.SubstanceModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SubsystemsModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.SubsystemsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TLSModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.TLSModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TerrainModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.TerrainModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TerrainPhysicsModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.TerrainPhysicsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextCoreFontEngineModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.TextCoreFontEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextCoreTextEngineModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.TextCoreTextEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextRenderingModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.TextRenderingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TilemapModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.TilemapModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UIModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UIElementsModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UIElementsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UmbraModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UmbraModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityAnalyticsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsCommonModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityAnalyticsCommonModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityConnectModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityConnectModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityCurlModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityCurlModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityTestProtocolModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityTestProtocolModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAssetBundleModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestAssetBundleModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAudioModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestAudioModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestTextureModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestTextureModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestWWWModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestWWWModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VFXModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.VFXModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VRModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.VRModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VehiclesModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.VehiclesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VideoModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.VideoModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VirtualTexturingModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.VirtualTexturingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.WindModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.WindModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.XRModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.XRModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.CoreModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.CoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.DeviceSimulatorModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.DeviceSimulatorModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.DiagnosticsModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.DiagnosticsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.EditorToolbarModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.EditorToolbarModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GraphViewModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.GraphViewModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.PresetsUIModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.PresetsUIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.QuickSearchModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.QuickSearchModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SceneTemplateModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.SceneTemplateModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SceneViewModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.SceneViewModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TextCoreFontEngineModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.TextCoreFontEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TextCoreTextEngineModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.TextCoreTextEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIBuilderModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.UIBuilderModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIElementsModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.UIElementsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIElementsSamplesModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.UIElementsSamplesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UnityConnectModule">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.UnityConnectModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.Graphs">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\Managed\UnityEditor.Graphs.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.WindowsStandalone.Extensions">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\PlaybackEngines\WindowsStandaloneSupport\UnityEditor.WindowsStandalone.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DemiEditor">
      <HintPath>Assets\Plugins\Demigiant\DemiLib\Core\Editor\DemiEditor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.YamlDotNet">
      <HintPath>Library\PackageCache\com.unity.visualscripting@1.9.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Plastic.Antlr3.Runtime">
      <HintPath>Library\PackageCache\com.unity.collab-proxy@2.9.2\Lib\Editor\Unity.Plastic.Antlr3.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DemiLib">
      <HintPath>Assets\Plugins\Demigiant\DemiLib\Core\DemiLib.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DOTweenEditor">
      <HintPath>Assets\Plugins\Demigiant\DOTween\Editor\DOTweenEditor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Plastic.Newtonsoft.Json">
      <HintPath>Library\PackageCache\com.unity.collab-proxy@2.9.2\Lib\Editor\Unity.Plastic.Newtonsoft.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DOTweenProEditor">
      <HintPath>Assets\Plugins\Demigiant\DOTweenPro\Editor\DOTweenProEditor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="log4netPlastic">
      <HintPath>Library\PackageCache\com.unity.collab-proxy@2.9.2\Lib\Editor\log4netPlastic.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.IonicZip">
      <HintPath>Library\PackageCache\com.unity.visualscripting@1.9.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="unityplastic">
      <HintPath>Library\PackageCache\com.unity.collab-proxy@2.9.2\Lib\Editor\unityplastic.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DOTween">
      <HintPath>Assets\Plugins\Demigiant\DOTween\DOTween.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.Antlr3.Runtime">
      <HintPath>Library\PackageCache\com.unity.visualscripting@1.9.8\Runtime\VisualScripting.Flow\Dependencies\NCalc\Unity.VisualScripting.Antlr3.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DOTweenPro">
      <HintPath>Assets\Plugins\Demigiant\DOTweenPro\DOTweenPro.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RainbowFolders">
      <HintPath>Assets\Plugins\Borodar\RainbowFolders\Editor\Assemblies\RainbowFolders.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Newtonsoft.Json">
      <HintPath>Library\PackageCache\com.unity.nuget.newtonsoft-json@3.2.1\Runtime\Newtonsoft.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.TextureAssets">
      <HintPath>Library\PackageCache\com.unity.visualscripting@1.9.8\Editor\VisualScripting.Core\EditorAssetResources\Unity.VisualScripting.TextureAssets.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Harmony">
      <HintPath>Assets\Plugins\Infinity Code\Ultimate Editor Enhancer\Libraries\Harmony.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="nunit.framework">
      <HintPath>Library\PackageCache\com.unity.ext.nunit@1.0.6\net35\unity-custom\nunit.framework.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="mscorlib">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\mscorlib.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Core">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Runtime.Serialization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Xml.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.Linq">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Xml.Linq.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Numerics">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Numerics.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Numerics.Vectors">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Numerics.Vectors.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Http">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Net.Http.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.IO.Compression.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.CSharp">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Microsoft.CSharp.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Data.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data.DataSetExtensions">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Data.DataSetExtensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Drawing">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Drawing.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression.FileSystem">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.IO.Compression.FileSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Composition">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.ComponentModel.Composition.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Transactions">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Transactions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Win32.Primitives">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\Microsoft.Win32.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="netstandard">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\netstandard.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.AppContext">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.AppContext.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Buffers">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Buffers.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.Concurrent">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Collections.Concurrent.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Collections.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.NonGeneric">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Collections.NonGeneric.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.Specialized">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Collections.Specialized.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Annotations">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.Annotations.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.EventBasedAsync">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.EventBasedAsync.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Primitives">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.TypeConverter">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.TypeConverter.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Console">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Console.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data.Common">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Data.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Contracts">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.Contracts.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Debug">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.Debug.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.FileVersionInfo">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.FileVersionInfo.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Process">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.Process.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.StackTrace">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.StackTrace.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.TextWriterTraceListener">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.TextWriterTraceListener.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Tools">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.Tools.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.TraceSource">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.TraceSource.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Drawing.Primitives">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Drawing.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Dynamic.Runtime">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Dynamic.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization.Calendars">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Globalization.Calendars.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Globalization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization.Extensions">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Globalization.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression.ZipFile">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.Compression.ZipFile.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.FileSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.DriveInfo">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.FileSystem.DriveInfo.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.Primitives">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.FileSystem.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.Watcher">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.FileSystem.Watcher.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.IsolatedStorage">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.IsolatedStorage.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.MemoryMappedFiles">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.MemoryMappedFiles.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Pipes">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.Pipes.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.UnmanagedMemoryStream">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.UnmanagedMemoryStream.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Linq.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Expressions">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Linq.Expressions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Parallel">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Linq.Parallel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Queryable">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Linq.Queryable.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Memory">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Memory.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Http.Rtc">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Http.Rtc.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.NameResolution">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.NameResolution.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.NetworkInformation">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.NetworkInformation.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Ping">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Ping.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Primitives">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Requests">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Requests.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Security">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Security.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Sockets">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Sockets.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebHeaderCollection">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.WebHeaderCollection.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebSockets.Client">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.WebSockets.Client.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebSockets">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.WebSockets.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ObjectModel">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ObjectModel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.DispatchProxy">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.DispatchProxy.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Emit.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit.ILGeneration">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Emit.ILGeneration.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit.Lightweight">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Emit.Lightweight.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Extensions">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Primitives">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.Reader">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Resources.Reader.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.ResourceManager">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Resources.ResourceManager.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.Writer">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Resources.Writer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.VisualC">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.CompilerServices.VisualC.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Extensions">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Handles">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Handles.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.InteropServices.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.RuntimeInformation">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.WindowsRuntime">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.InteropServices.WindowsRuntime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Numerics">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Numerics.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Formatters">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Serialization.Formatters.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Json">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Serialization.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Primitives">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Serialization.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Xml">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Serialization.Xml.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Claims">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Claims.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Algorithms">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.Algorithms.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Csp">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.Csp.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.Encoding.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.X509Certificates.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Principal">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Principal.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.SecureString">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.SecureString.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.Duplex">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.Duplex.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.Http">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.Http.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.NetTcp">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.NetTcp.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.Primitives">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.Security">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.Security.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.Encoding">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Text.Encoding.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.Encoding.Extensions">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Text.Encoding.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.RegularExpressions">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Text.RegularExpressions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Overlapped">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Overlapped.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Tasks.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Tasks.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks.Parallel">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Tasks.Parallel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Thread">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Thread.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.ThreadPool">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.ThreadPool.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Timer">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Timer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ValueTuple">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ValueTuple.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.ReaderWriter">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.ReaderWriter.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XDocument">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XmlDocument">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XmlDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XmlSerializer">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XmlSerializer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XPath">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XPath.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XPath.XDocument">
      <HintPath>D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XPath.XDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UI">
      <HintPath>Library\ScriptAssemblies\UnityEditor.UI.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UI">
      <HintPath>Library\ScriptAssemblies\UnityEngine.UI.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TestRunner">
      <HintPath>Library\ScriptAssemblies\UnityEditor.TestRunner.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TestRunner">
      <HintPath>Library\ScriptAssemblies\UnityEngine.TestRunner.dll</HintPath>
      <Private>False</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="UltimateEditorEnhancer.csproj" />
  </ItemGroup>
  <Import Project="Sdk.targets" Sdk="Microsoft.NET.Sdk" />
  <ItemGroup>
    <ProjectCapability Remove="LaunchProfiles" />
    <ProjectCapability Remove="SharedProjectReferences" />
    <ProjectCapability Remove="ReferenceManagerSharedProjects" />
    <ProjectCapability Remove="ReferenceManagerProjects" />
    <ProjectCapability Remove="COMReferences" />
    <ProjectCapability Remove="ReferenceManagerCOM" />
    <ProjectCapability Remove="AssemblyReferences" />
    <ProjectCapability Remove="ReferenceManagerAssemblies" />
  </ItemGroup>
</Project>
