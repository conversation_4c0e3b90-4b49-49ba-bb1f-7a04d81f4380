<ui:UXML xmlns:ui="UnityEngine.UIElements" xmlns:uie="UnityEditor.UIElements" editor-extension-mode="True">
    <Style src="project://database/Assets/Scripts/DialogueTree/Editor/DialogueViewer.uss?fileID=7433441132597879392&amp;guid=e58debb45e2f3354baf0d421cbc1034a&amp;type=3#DialogueViewer" />
    <ui:VisualElement style="flex-grow: 1; align-items: stretch; align-self: stretch; justify-content: flex-start;">
        <ui:VisualElement name="input" style="flex-grow: 1; min-width: 72px; align-items: center; align-self: auto; margin-right: 3px; margin-left: 3px; justify-content: center;" />
        <ui:VisualElement name="node-border">
            <ui:VisualElement name="contents" style="border-right-width: 0; border-left-width: 0; border-top-width: 0; border-bottom-width: 0;">
                <ui:VisualElement name="top">
                    <ui:VisualElement name="divider" class="horizontal" />
                    <ui:VisualElement name="title" style="margin-right: 0; margin-left: 0; margin-top: 0; margin-bottom: 0;">
                        <ui:Label name="title-label" class="unity-text-element unity-label" style="-unity-text-outline-color: rgba(0, 0, 0, 0);" />
                        <ui:VisualElement name="title-button-container">
                            <ui:VisualElement name="collapse-button">
                                <ui:VisualElement name="icon" />
                            </ui:VisualElement>
                        </ui:VisualElement>
                    </ui:VisualElement>
                    <ui:VisualElement name="divider" class="vertical" />
                </ui:VisualElement>
            </ui:VisualElement>
            <ui:VisualElement name="ContentUI" style="flex-grow: 0; flex-direction: row; margin-right: 0; margin-left: 0;">
                <ui:VisualElement name="LeftImage" style="flex-grow: 0; max-width: 36px; max-height: 60px; align-items: stretch; align-self: auto; margin-top: 0; margin-right: 0; margin-bottom: 0; margin-left: 0;" />
                <ui:VisualElement name="MiddleView" style="flex-grow: 1; max-width: none;">
                    <ui:VisualElement name="TextContent" style="flex-grow: 0; max-width: 256px;" />
                </ui:VisualElement>
                <ui:VisualElement name="RightImage" style="flex-grow: 0; max-width: 36px; max-height: 60px;" />
            </ui:VisualElement>
            <ui:VisualElement name="FunctionButtons" style="align-self: stretch; justify-content: space-between; align-items: stretch; max-width: none; width: auto;" />
        </ui:VisualElement>
        <ui:VisualElement name="outputFun" style="flex-grow: 0; flex-direction: row; align-self: center;">
            <ui:VisualElement name="CondOutput" style="flex-grow: 0; background-color: rgba(204, 45, 45, 0.71); border-bottom-right-radius: 3px; border-bottom-left-radius: 3px; max-height: 6px;" />
            <ui:VisualElement name="output" />
        </ui:VisualElement>
    </ui:VisualElement>
    <ui:VisualElement name="selection-border" picking-mode="Ignore" style="background-color: rgba(0, 0, 0, 0);" />
</ui:UXML>
